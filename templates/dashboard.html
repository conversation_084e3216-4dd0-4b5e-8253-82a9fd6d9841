<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure Sentinel Lite - Security Dashboard</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>

    <style>
        .severity-critical { color: #dc3545; }
        .severity-high { color: #fd7e14; }
        .severity-medium { color: #ffc107; }
        .severity-low { color: #28a745; }

        .alert-card {
            border-left: 4px solid #007bff;
            margin-bottom: 10px;
        }

        .alert-card.critical { border-left-color: #dc3545; }
        .alert-card.high { border-left-color: #fd7e14; }
        .alert-card.medium { border-left-color: #ffc107; }
        .alert-card.low { border-left-color: #28a745; }

        .stat-card {
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .monitoring-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .chart-container {
            height: 400px;
            margin-bottom: 20px;
        }

        .navbar-brand {
            font-weight: bold;
        }

        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }

        .main-content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt"></i> Azure Sentinel Lite
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-clock"></i> <span id="current-time"></span>
                </span>
            </div>
        </div>
    </nav>

    <!-- Monitoring Status Badge -->
    <div class="monitoring-status">
        <span id="monitoring-badge" class="badge bg-secondary">
            <i class="fas fa-pause"></i> Monitoring Stopped
        </span>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar p-3">
                <h6 class="text-muted">ACTIONS</h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="runDetection('all')">
                        <i class="fas fa-search"></i> Run All Detectors
                    </button>
                    <button class="btn btn-outline-success btn-sm" id="monitoring-btn" onclick="toggleMonitoring()">
                        <i class="fas fa-play"></i> Start Monitoring
                    </button>
                </div>

                <hr>
                <h6 class="text-muted">DETECTORS</h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="runDetection('port_scan')">
                        <i class="fas fa-network-wired"></i> Port Scan
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="runDetection('exposed_vm')">
                        <i class="fas fa-server"></i> Exposed VMs
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="runDetection('unusual_signin')">
                        <i class="fas fa-globe"></i> Unusual Sign-ins
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="runDetection('elevated_activity')">
                        <i class="fas fa-crown"></i> Elevated Activity
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="runDetection('privilege_escalation')">
                        <i class="fas fa-arrow-up"></i> Privilege Escalation
                    </button>
                </div>

                <hr>
                <h6 class="text-muted">MAINTENANCE</h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-danger btn-sm" onclick="clearData()">
                        <i class="fas fa-trash"></i> Clear Logs
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="rotateLogs()">
                        <i class="fas fa-archive"></i> Archive Old Logs
                    </button>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="total-alerts">{{ stats.total_alerts }}</h4>
                                        <p class="mb-0">Total Alerts</p>
                                    </div>
                                    <div>
                                        <i class="fas fa-bell fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="recent-alerts">{{ stats.recent_alerts_count }}</h4>
                                        <p class="mb-0">Recent Alerts (24h)</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="total-events">{{ stats.total_events }}</h4>
                                        <p class="mb-0">Total Events</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-list fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="critical-alerts">{{ stats.severity_counts.get('CRITICAL', 0) }}</h4>
                                        <p class="mb-0">Critical Alerts</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-fire fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Alert Severity Distribution</h6>
                            </div>
                            <div class="card-body">
                                <div id="severity-chart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Alert Types</h6>
                            </div>
                            <div class="card-body">
                                <div id="types-chart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-chart-line"></i> Alert Timeline</h6>
                            </div>
                            <div class="card-body">
                                <div id="timeline-chart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Alerts and Top Users -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-bell"></i> Recent Alerts</h6>
                            </div>
                            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                <div id="recent-alerts-list">
                                    {% for alert in stats.recent_alerts %}
                                    <div class="alert-card card {{ alert.severity|lower if alert.severity else 'secondary' }}">
                                        <div class="card-body py-2">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <span class="badge bg-{{ 'danger' if alert.severity == 'CRITICAL' else 'warning' if alert.severity == 'HIGH' else 'info' if alert.severity == 'MEDIUM' else 'success' }}">
                                                            {{ alert.severity or 'UNKNOWN' }}
                                                        </span>
                                                        {{ alert.type|replace('_', ' ')|title }}
                                                    </h6>
                                                    <p class="mb-1 small">{{ alert.message }}</p>
                                                    {% if alert.user %}
                                                    <small class="text-muted">
                                                        <i class="fas fa-user"></i> {{ alert.user }}
                                                    </small>
                                                    {% endif %}
                                                </div>
                                                <small class="text-muted">
                                                    {{ alert.timestamp[:19] if alert.timestamp else 'Unknown' }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-users"></i> Top Affected Users</h6>
                            </div>
                            <div class="card-body">
                                <div id="top-users-list">
                                    {% for user, count in stats.top_users.items() %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>{{ user }}</span>
                                        <span class="badge bg-primary">{{ count }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notification-toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-bell text-primary"></i>
                <strong class="me-auto ms-2">Azure Sentinel Lite</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toast-message">
                <!-- Toast message will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Initialize Socket.IO
        const socket = io();
        let monitoringActive = false;

        // Initialize charts
        const severityChart = JSON.parse('{{ charts.severity_chart|safe }}');
        const typesChart = JSON.parse('{{ charts.types_chart|safe }}');
        const timelineChart = JSON.parse('{{ charts.timeline_chart|safe }}');

        // Plot charts with proper configurations
        const plotlyConfig = {
            responsive: true,
            displayModeBar: false
        };

        Plotly.newPlot('severity-chart', severityChart.data, severityChart.layout, plotlyConfig);
        Plotly.newPlot('types-chart', typesChart.data, typesChart.layout, plotlyConfig);
        Plotly.newPlot('timeline-chart', timelineChart.data, timelineChart.layout, plotlyConfig);

        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString();
        }
        setInterval(updateTime, 1000);
        updateTime();

        // Socket.IO event handlers
        socket.on('connect', function() {
            showToast('Connected to Azure Sentinel Lite', 'success');
        });

        socket.on('detection_complete', function(data) {
            showToast(`Detection completed: ${data.alerts_count} alerts generated`, 'info');
            refreshDashboard();
        });

        socket.on('simulation_complete', function(data) {
            showToast('Threat simulation completed', 'success');
            refreshDashboard();
        });

        socket.on('monitoring_update', function(data) {
            showToast(`Monitoring cycle completed: ${data.alerts_count} alerts`, 'info');
            refreshDashboard();
        });

        socket.on('monitoring_stopped', function(data) {
            monitoringActive = false;
            updateMonitoringUI();
            showToast('Monitoring stopped', 'warning');
        });

        // Functions
        function showToast(message, type = 'info') {
            const toast = document.getElementById('notification-toast');
            const toastMessage = document.getElementById('toast-message');

            toastMessage.textContent = message;

            // Update toast styling based on type
            toast.className = `toast border-${type === 'success' ? 'success' : type === 'warning' ? 'warning' : type === 'danger' ? 'danger' : 'info'}`;

            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        function runDetection(detector) {
            showToast(`Running ${detector} detection...`, 'info');

            fetch('/api/run_detection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({detector: detector})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                } else {
                    showToast(`Error: ${data.error}`, 'danger');
                }
            })
            .catch(error => {
                showToast(`Error: ${error}`, 'danger');
            });
        }

        function simulateThreats() {
            showToast('Generating threat simulation...', 'info');

            fetch('/api/simulate_threats', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                } else {
                    showToast(`Error: ${data.error}`, 'danger');
                }
            })
            .catch(error => {
                showToast(`Error: ${error}`, 'danger');
            });
        }

        function toggleMonitoring() {
            const endpoint = monitoringActive ? '/api/stop_monitoring' : '/api/start_monitoring';

            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    monitoringActive = !monitoringActive;
                    updateMonitoringUI();
                    showToast(data.message, 'success');
                } else {
                    showToast(`Error: ${data.error}`, 'danger');
                }
            })
            .catch(error => {
                showToast(`Error: ${error}`, 'danger');
            });
        }

        function updateMonitoringUI() {
            const badge = document.getElementById('monitoring-badge');
            const button = document.getElementById('monitoring-btn');

            if (monitoringActive) {
                badge.innerHTML = '<i class="fas fa-play"></i> Monitoring Active';
                badge.className = 'badge bg-success';
                button.innerHTML = '<i class="fas fa-stop"></i> Stop Monitoring';
                button.className = 'btn btn-outline-danger btn-sm';
            } else {
                badge.innerHTML = '<i class="fas fa-pause"></i> Monitoring Stopped';
                badge.className = 'badge bg-secondary';
                button.innerHTML = '<i class="fas fa-play"></i> Start Monitoring';
                button.className = 'btn btn-outline-success btn-sm';
            }
        }

        function refreshDashboard() {
            location.reload();
        }

        function clearTestData() {
            showToast('Clearing test data...', 'info');

            fetch('/api/clear_test_data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Test data cleared', 'success');
                    refreshDashboard();
                } else {
                    showToast(`Error: ${data.error}`, 'danger');
                }
            })
            .catch(error => {
                showToast(`Error: ${error}`, 'danger');
            });
        }

        function generateRandomEvents() {
            showToast('Generating random events...', 'info');

            fetch('/api/generate_events', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Random events generated', 'success');
                    refreshDashboard();
                } else {
                    showToast(`Error: ${data.error}`, 'danger');
                }
            })
            .catch(error => {
                showToast(`Error: ${error}`, 'danger');
            });
        }



        function clearData() {
            if (confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
                showToast('Clearing logs...', 'info');

                fetch('/api/clear_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success');
                        refreshDashboard();
                    } else {
                        showToast(`Error: ${data.error}`, 'danger');
                    }
                })
                .catch(error => {
                    showToast(`Error: ${error}`, 'danger');
                });
            }
        }

        function rotateLogs() {
            showToast('Archiving old logs...', 'info');

            fetch('/api/rotate_logs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({days: 30})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    refreshDashboard();
                } else {
                    showToast(`Error: ${data.error}`, 'danger');
                }
            })
            .catch(error => {
                showToast(`Error: ${error}`, 'danger');
            });
        }

        setInterval(function() {
            if (!monitoringActive) {
                fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    // Update statistics
                    document.getElementById('total-alerts').textContent = data.total_alerts;
                    document.getElementById('recent-alerts').textContent = data.recent_alerts_count;
                    document.getElementById('total-events').textContent = data.total_events;
                    document.getElementById('critical-alerts').textContent = data.severity_counts.CRITICAL || 0;
                });
            }
        }, 30000);
    </script>
</body>
</html>
