#!/usr/bin/env python3
"""
Comprehensive test script for Azure Sentinel Lite
Tests the complete system without demo/simulation components
"""

import sys
import subprocess
import time
from pathlib import Path

# Add local modules to path
sys.path.append(str(Path(__file__).parent))

from utils.config import Config

def test_main_application():
    """Test the main application"""
    print("🔍 Testing main application...")
    
    try:
        result = subprocess.run([
            sys.executable, 'azure_sentinel_lite.py'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Main application runs successfully")
            if "Total Alerts:" in result.stdout:
                print("✅ Detection summary generated")
            return True
        else:
            print(f"❌ Main application failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Main application timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing main application: {e}")
        return False

def test_individual_detectors():
    """Test individual detector execution"""
    print("\n🔍 Testing individual detectors...")
    
    detectors = ['port_scan', 'exposed_vm', 'unusual_signin', 'elevated_activity']
    results = {}
    
    for detector in detectors:
        print(f"   Testing {detector} detector...")
        try:
            result = subprocess.run([
                sys.executable, 'azure_sentinel_lite.py', '--detector', detector
            ], capture_output=True, text=True, timeout=20)
            
            if result.returncode == 0:
                print(f"   ✅ {detector} detector works")
                results[detector] = True
            else:
                print(f"   ❌ {detector} detector failed: {result.stderr}")
                results[detector] = False
                
        except subprocess.TimeoutExpired:
            print(f"   ❌ {detector} detector timed out")
            results[detector] = False
        except Exception as e:
            print(f"   ❌ Error testing {detector} detector: {e}")
            results[detector] = False
    
    return results

def test_recent_alerts():
    """Test recent alerts functionality"""
    print("\n🔍 Testing recent alerts...")
    
    try:
        result = subprocess.run([
            sys.executable, 'azure_sentinel_lite.py', '--recent', '5'
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ Recent alerts functionality works")
            return True
        else:
            print(f"❌ Recent alerts failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Recent alerts timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing recent alerts: {e}")
        return False

def test_help_functionality():
    """Test help functionality"""
    print("\n🔍 Testing help functionality...")
    
    try:
        result = subprocess.run([
            sys.executable, 'azure_sentinel_lite.py', '--help'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and '--detector' in result.stdout:
            print("✅ Help functionality works")
            # Check that simulate option is not present
            if '--simulate' not in result.stdout:
                print("✅ Simulation option successfully removed")
                return True
            else:
                print("❌ Simulation option still present in help")
                return False
        else:
            print(f"❌ Help functionality failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing help: {e}")
        return False

def test_detector_test_script():
    """Test the detector test script"""
    print("\n🔍 Testing detector test script...")
    
    try:
        result = subprocess.run([
            sys.executable, 'test_detectors.py'
        ], capture_output=True, text=True, timeout=45)
        
        if result.returncode == 0:
            print("✅ Detector test script works")
            if "alerts generated" in result.stdout:
                print("✅ Test script generates alerts")
            return True
        else:
            print(f"❌ Detector test script failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Detector test script timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing detector script: {e}")
        return False

def test_file_structure():
    """Test that demo/simulation files are removed"""
    print("\n🔍 Testing file structure...")
    
    removed_paths = [
        'simulator',
        'test_real_scenarios.py',
        'logs/demo_data'
    ]
    
    all_removed = True
    for path in removed_paths:
        if Path(path).exists():
            print(f"❌ {path} still exists (should be removed)")
            all_removed = False
        else:
            print(f"✅ {path} successfully removed")
    
    return all_removed

def test_log_files():
    """Test log file creation and structure"""
    print("\n🔍 Testing log files...")
    
    # Ensure log directory exists
    Config.LOG_DIR.mkdir(parents=True, exist_ok=True)
    
    required_logs = [Config.EVENTS_LOG, Config.ALERTS_LOG]
    
    for log_file in required_logs:
        if log_file.exists():
            print(f"✅ {log_file.name} exists")
        else:
            print(f"⚠️ {log_file.name} doesn't exist (will be created on first use)")
    
    return True

def main():
    """Run comprehensive system tests"""
    print("🚀 AZURE SENTINEL LITE - COMPREHENSIVE SYSTEM TEST")
    print("=" * 70)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Log Files", test_log_files),
        ("Detector Test Script", test_detector_test_script),
        ("Main Application", test_main_application),
        ("Individual Detectors", test_individual_detectors),
        ("Recent Alerts", test_recent_alerts),
        ("Help Functionality", test_help_functionality),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<50} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Azure Sentinel Lite is ready for use.")
        return 0
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
