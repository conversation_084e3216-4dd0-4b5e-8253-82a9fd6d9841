#!/usr/bin/env python3
"""
Simulates realistic security incidents for testing Azure Sentinel Lite
"""

import json
import time
import random
from datetime import datetime, timezone, timedelta
from pathlib import Path
import sys

# Add local modules to path
sys.path.append(str(Path(__file__).parent))

from utils.config import Config
from utils.file_operations import safe_write_jsonl
from azure_sentinel_lite import AzureSentinelLite

class RealWorldTester:
    """Real-world security scenario tester"""
    
    def __init__(self):
        self.config = Config
        self.sentinel = AzureSentinelLite()
        
        # Real-world IP addresses and locations
        self.suspicious_ips = [
            {"ip": "***************", "location": {"city": "Moscow", "country": "Russia", "lat": 55.7558, "lon": 37.6176}},
            {"ip": "***************", "location": {"city": "Beijing", "country": "China", "lat": 39.9042, "lon": 116.4074}},
            {"ip": "************", "location": {"city": "Bucharest", "country": "Romania", "lat": 44.4268, "lon": 26.1025}},
            {"ip": "**************", "location": {"city": "Unknown", "country": "Unknown", "lat": 0, "lon": 0}},
            {"ip": "*************", "location": {"city": "Amsterdam", "country": "Netherlands", "lat": 52.3676, "lon": 4.9041}}
        ]
        
        # Legitimate corporate locations
        self.corporate_locations = [
            {"city": "Seattle", "country": "USA", "lat": 47.6062, "lon": -122.3321},
            {"city": "London", "country": "UK", "lat": 51.5074, "lon": -0.1278},
            {"city": "Sydney", "country": "Australia", "lat": -33.8688, "lon": 151.2093}
        ]
        
        # Real user accounts
        self.users = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        # Azure roles with realistic risk levels
        self.azure_roles = {
            "Reader": 1,
            "Contributor": 4,
            "Owner": 8,
            "User Access Administrator": 7,
            "Security Administrator": 6,
            "Global Administrator": 10
        }

    def scenario_1_credential_stuffing_attack(self):
        """
        Scenario 1: Credential Stuffing Attack
        Simulates multiple failed login attempts from various IPs followed by successful login
        """
        print("\n🎯 SCENARIO 1: Credential Stuffing Attack")
        print("=" * 60)
        
        target_user = "<EMAIL>"
        
        # Generate failed login attempts from suspicious IPs
        events = []
        for i in range(15):
            suspicious_ip = random.choice(self.suspicious_ips)
            event = {
                "timestamp": (datetime.now(timezone.utc) - timedelta(minutes=30-i)).isoformat(),
                "type": "login",
                "user": target_user,
                "ip_address": suspicious_ip["ip"],
                "location": suspicious_ip["location"],
                "success": False,
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "source": "Azure AD"
            }
            events.append(event)
        
        # Successful login from legitimate location
        corp_location = random.choice(self.corporate_locations)
        success_event = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "type": "login", 
            "user": target_user,
            "ip_address": "************",  # Corporate IP
            "location": corp_location,
            "success": True,
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "source": "Azure AD"
        }
        events.append(success_event)
        
        # Write events to log
        for event in events:
            safe_write_jsonl(Config.EVENTS_LOG, event)
        
        print(f"✅ Generated {len(events)} login events")
        print(f"   - {len(events)-1} failed attempts from suspicious IPs")
        print(f"   - 1 successful login from corporate location")
        
        return events

    def scenario_2_privilege_escalation_attack(self):
        """
        Scenario 2: Privilege Escalation Attack
        Simulates rapid privilege escalation by compromised account
        """
        print("\n🎯 SCENARIO 2: Privilege Escalation Attack")
        print("=" * 60)
        
        compromised_user = "<EMAIL>"
        escalation_path = ["Reader", "Contributor", "User Access Administrator", "Owner"]
        
        events = []
        base_time = datetime.now(timezone.utc) - timedelta(minutes=10)
        
        for i, role in enumerate(escalation_path):
            event = {
                "timestamp": (base_time + timedelta(minutes=i*2)).isoformat(),
                "type": "role_change",
                "user": compromised_user,
                "old_role": escalation_path[i-1] if i > 0 else "None",
                "new_role": role,
                "changed_by": "<EMAIL>" if i < 2 else compromised_user,
                "ip_address": "***************",  # Suspicious IP
                "source": "Azure RBAC"
            }
            events.append(event)
        
        # Write events to log
        for event in events:
            safe_write_jsonl(Config.EVENTS_LOG, event)
        
        print(f"✅ Generated {len(events)} privilege escalation events")
        print(f"   - User escalated from Reader to Owner in {len(events)*2} minutes")
        print(f"   - Final escalation performed by compromised user")
        
        return events

    def scenario_3_impossible_travel(self):
        """
        Scenario 3: Impossible Travel
        Simulates user logging in from geographically impossible locations
        """
        print("\n🎯 SCENARIO 3: Impossible Travel")
        print("=" * 60)
        
        user = "<EMAIL>"
        
        # Login from New York
        ny_event = {
            "timestamp": (datetime.now(timezone.utc) - timedelta(minutes=30)).isoformat(),
            "type": "login",
            "user": user,
            "ip_address": "**************",
            "location": {"city": "New York", "country": "USA", "lat": 40.7128, "lon": -74.0060},
            "success": True,
            "source": "Azure AD"
        }
        
        # Login from Tokyo 20 minutes later (impossible travel)
        tokyo_event = {
            "timestamp": (datetime.now(timezone.utc) - timedelta(minutes=10)).isoformat(),
            "type": "login",
            "user": user,
            "ip_address": "***************",
            "location": {"city": "Tokyo", "country": "Japan", "lat": 35.6762, "lon": 139.6503},
            "success": True,
            "source": "Azure AD"
        }
        
        events = [ny_event, tokyo_event]
        
        # Write events to log
        for event in events:
            safe_write_jsonl(Config.EVENTS_LOG, event)
        
        print(f"✅ Generated {len(events)} impossible travel events")
        print(f"   - Login from New York at {ny_event['timestamp'][:19]}")
        print(f"   - Login from Tokyo at {tokyo_event['timestamp'][:19]}")
        print(f"   - Travel time: 20 minutes (impossible)")
        
        return events

    def scenario_4_exposed_vm_exploitation(self):
        """
        Scenario 4: Exposed VM Exploitation
        Simulates network scanning and exploitation of exposed VMs
        """
        print("\n🎯 SCENARIO 4: Exposed VM Exploitation")
        print("=" * 60)
        
        target_vm_ip = "************"
        attacker_ip = "**************"
        
        # Port scanning activity
        scan_events = []
        common_ports = [22, 23, 25, 53, 80, 110, 135, 139, 443, 993, 995, 1723, 3389, 5900]
        
        base_time = datetime.now(timezone.utc) - timedelta(minutes=45)
        
        for i, port in enumerate(common_ports):
            event = {
                "timestamp": (base_time + timedelta(seconds=i*5)).isoformat(),
                "type": "network_access",
                "source_ip": attacker_ip,
                "destination_ip": target_vm_ip,
                "port": port,
                "protocol": "TCP",
                "action": "SYN",
                "result": "blocked" if port not in [80, 443, 3389] else "allowed"
            }
            scan_events.append(event)
        
        # Successful RDP connection
        rdp_event = {
            "timestamp": (base_time + timedelta(minutes=5)).isoformat(),
            "type": "network_access",
            "source_ip": attacker_ip,
            "destination_ip": target_vm_ip,
            "port": 3389,
            "protocol": "TCP",
            "action": "CONNECT",
            "result": "allowed",
            "duration": 3600  # 1 hour session
        }
        scan_events.append(rdp_event)
        
        # Write events to log
        for event in scan_events:
            safe_write_jsonl(Config.EVENTS_LOG, event)
        
        print(f"✅ Generated {len(scan_events)} network events")
        print(f"   - Port scan of {len(common_ports)} ports")
        print(f"   - Successful RDP connection on port 3389")
        print(f"   - Attacker IP: {attacker_ip}")
        
        return scan_events

    def scenario_5_after_hours_activity(self):
        """
        Scenario 5: After-Hours Suspicious Activity
        Simulates suspicious administrative activity during off-hours
        """
        print("\n🎯 SCENARIO 5: After-Hours Suspicious Activity")
        print("=" * 60)
        
        # Set time to 2 AM (after hours)
        after_hours = datetime.now(timezone.utc).replace(hour=2, minute=30, second=0, microsecond=0)
        
        events = []
        
        # Admin login at unusual time
        login_event = {
            "timestamp": after_hours.isoformat(),
            "type": "login",
            "user": "<EMAIL>",
            "ip_address": "************",  # Suspicious IP
            "location": {"city": "Bucharest", "country": "Romania", "lat": 44.4268, "lon": 26.1025},
            "success": True,
            "source": "Azure AD"
        }
        events.append(login_event)
        
        # Bulk user creation
        for i in range(5):
            user_creation = {
                "timestamp": (after_hours + timedelta(minutes=i*2)).isoformat(),
                "type": "user_management",
                "action": "create_user",
                "target_user": f"temp-user-{i+1}@corp.com",
                "performed_by": "<EMAIL>",
                "ip_address": "************",
                "source": "Azure AD"
            }
            events.append(user_creation)
        
        # Privilege assignments
        for i in range(3):
            role_assignment = {
                "timestamp": (after_hours + timedelta(minutes=10+i)).isoformat(),
                "type": "role_change",
                "user": f"temp-user-{i+1}@corp.com",
                "old_role": "None",
                "new_role": "Contributor",
                "changed_by": "<EMAIL>",
                "ip_address": "************",
                "source": "Azure RBAC"
            }
            events.append(role_assignment)
        
        # Write events to log
        for event in events:
            safe_write_jsonl(Config.EVENTS_LOG, event)
        
        print(f"✅ Generated {len(events)} after-hours events")
        print(f"   - Admin login from suspicious location at 2:30 AM")
        print(f"   - Created 5 new user accounts")
        print(f"   - Assigned privileges to 3 accounts")
        
        return events

    def run_all_scenarios(self):
        """Run all real-world security scenarios"""
        print("🚀 STARTING REAL-WORLD SECURITY TESTING")
        print("=" * 80)
        
        all_events = []
        
        # Run each scenario
        all_events.extend(self.scenario_1_credential_stuffing_attack())
        time.sleep(1)
        
        all_events.extend(self.scenario_2_privilege_escalation_attack())
        time.sleep(1)
        
        all_events.extend(self.scenario_3_impossible_travel())
        time.sleep(1)
        
        all_events.extend(self.scenario_4_exposed_vm_exploitation())
        time.sleep(1)
        
        all_events.extend(self.scenario_5_after_hours_activity())
        
        print(f"\n📊 SCENARIO SUMMARY")
        print("=" * 60)
        print(f"Total events generated: {len(all_events)}")
        print(f"Event types: {len(set(event['type'] for event in all_events))}")
        print(f"Time span: {(datetime.now(timezone.utc) - datetime.fromisoformat(min(event['timestamp'] for event in all_events).replace('Z', '+00:00'))).total_seconds()/60:.1f} minutes")
        
        return all_events

    def run_detection_analysis(self):
        """Run detection analysis on generated events"""
        print(f"\n🔍 RUNNING DETECTION ANALYSIS")
        print("=" * 60)
        
        # Run all detectors
        alerts = self.sentinel.run_all_detectors()
        
        print(f"\n📋 DETECTION RESULTS")
        print("=" * 60)
        print(f"Total alerts generated: {len(alerts)}")
        
        # Group by severity
        severity_counts = {}
        for alert in alerts:
            severity = alert.get('severity', 'UNKNOWN')
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        print(f"\nAlerts by severity:")
        for severity, count in sorted(severity_counts.items()):
            print(f"  {severity}: {count}")
        
        # Group by detector
        detector_counts = {}
        for alert in alerts:
            detector = alert.get('type', 'unknown')
            detector_counts[detector] = detector_counts.get(detector, 0) + 1
        
        print(f"\nAlerts by detector:")
        for detector, count in sorted(detector_counts.items()):
            print(f"  {detector}: {count}")
        
        return alerts

def main():
    """Main testing function"""
    print("🔒 Azure Sentinel Lite - Real-World Security Testing")
    print("=" * 80)
    
    # Initialize tester
    tester = RealWorldTester()
    
    # Clear existing logs for clean test
    if Config.EVENTS_LOG.exists():
        Config.EVENTS_LOG.unlink()
    if Config.ALERTS_LOG.exists():
        Config.ALERTS_LOG.unlink()
    
    # Create log directory
    Config.LOG_DIR.mkdir(parents=True, exist_ok=True)
    
    # Run scenarios
    events = tester.run_all_scenarios()
    
    # Run detection analysis
    alerts = tester.run_detection_analysis()
    
    print(f"\n✅ TESTING COMPLETE")
    print("=" * 60)
    print(f"Events generated: {len(events)}")
    print(f"Alerts generated: {len(alerts)}")
    print(f"Detection rate: {len(alerts)/len(events)*100:.1f}%")
    
    print(f"\n📁 Log files:")
    print(f"  Events: {Config.EVENTS_LOG}")
    print(f"  Alerts: {Config.ALERTS_LOG}")

if __name__ == "__main__":
    main()
