# Azure Sentinel Lite 🔒

A comprehensive, enterprise-grade real-time security detection system for Azure environments with advanced threat monitoring, secure alerting, and robust error handling.

## Features

### 🔍 Enhanced Detection Capabilities
- **Port Scan Detection** - Identifies network reconnaissance activities with pattern analysis
- **Exposed VM Detection** - Finds VMs with risky public IP configurations and NSG rules
- **Unusual Sign-in Location** - Detects logins from suspicious geographic locations with timezone-aware analysis
- **Elevated User Activity** - Monitors privileged user actions with behavioral analysis
- **Privilege Escalation** - Tracks unauthorized role changes with risk scoring

### 🔒 Enterprise Security Features
- **API Authentication** - Secure API endpoints with key-based authentication
- **Rate Limiting** - Protection against DoS attacks (10 requests/minute)
- **Input Validation** - JSON schema validation and sanitization
- **Path Security** - Directory traversal protection and filename sanitization
- **Subprocess Security** - Secure process execution with validation and timeouts
- **Network Security** - Retry logic, timeout protection, and HTTPS enforcement

### 📧 Advanced Alert System
- **Dual Notification Channels** - Azure Logic Apps (primary) + Email (fallback)
- **Azure Logic Apps Integration** - Enterprise workflow automation with Common Alert Schema
- **Secure Email Delivery** - SMTP with retry logic and proper error handling
- **Severity Classification** - CRITICAL, HIGH, MEDIUM, LOW risk levels with intelligent scoring
- **Detailed Reports** - Comprehensive alert information with actionable recommendations
- **Structured Logging** - Thread-safe JSON logging with automatic rotation

### 🌐 Secure Web Dashboard
- **Real-time Dashboard** - Interactive web interface with live updates and WebSocket support
- **Visual Analytics** - Charts and graphs for threat visualization with real-time data
- **Authenticated APIs** - Secure endpoints with API key authentication
- **Rate-Limited Access** - Protection against abuse with configurable limits
- **Live Monitoring** - Real-time threat detection with circuit breaker protection
- **Professional Interface** - Modern Bootstrap-based responsive design with security headers

### 🎯 Robust Azure Integration
- **Managed Identity Support** - Secure authentication with automatic credential management
- **Azure Key Vault Integration** - Secure secret storage and retrieval
- **Rate-Limited API Calls** - Pagination and throttling to prevent Azure API limits
- **Circuit Breaker Pattern** - Automatic failure detection and recovery
- **Real Azure Data** - Connects to Azure APIs with comprehensive error handling
- **RBAC Monitoring** - Tracks role-based access control changes with risk assessment

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd azure-sentinel-lite

# Create and activate virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install additional security dependencies
pip install pytz
```

### 2. Enhanced Configuration

The application now supports multiple configuration modes with enhanced security:

```bash
# Configuration file is already present
# Edit config.env with your settings
nano config.env
```

**Basic Configuration:**
```env
# Mode Configuration
FORCE_DEMO_MODE=true  # Set to false for production

# Azure Configuration
AZURE_SUBSCRIPTION_ID=your_subscription_id_here
AZURE_TENANT_ID=your_tenant_id_here
AZURE_CLIENT_ID=your_client_id_here
AZURE_CLIENT_SECRET=your_client_secret_here

# Email Configuration (Fallback)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
ADMIN_EMAIL=<EMAIL>
```

**Advanced Security Configuration:**
```env
# Azure Logic Apps Integration (Primary Alert Channel)
LOGIC_APP_WEBHOOK_URL=https://your-logic-app-url.azurewebsites.net/api/webhook
LOGIC_APP_RESOURCE_ID=/subscriptions/your-sub-id/resourceGroups/your-rg/providers/Microsoft.Logic/workflows/your-logic-app
USE_COMMON_ALERT_SCHEMA=true

# API Security (Required for Production)
API_SECRET_KEY=your-secure-api-key-here
WEBHOOK_SECRET=your-webhook-secret-here

# Detection Thresholds
ALERT_THRESHOLD_MINUTES=60
MAX_FAILED_LOGINS=5
UNUSUAL_LOCATION_THRESHOLD_KM=500
```

### 3. Azure Service Principal Setup (Optional - for Production)

For production deployment with real Azure monitoring:

```bash
# Create service principal with enhanced permissions
az ad sp create-for-rbac --name "azure-sentinel-lite" \
  --role "Security Reader" \
  --scopes "/subscriptions/YOUR_SUBSCRIPTION_ID"

# Additional roles needed for comprehensive monitoring:
az role assignment create --assignee <service-principal-id> \
  --role "Reader" \
  --scope "/subscriptions/YOUR_SUBSCRIPTION_ID"

az role assignment create --assignee <service-principal-id> \
  --role "Monitor Reader" \
  --scope "/subscriptions/YOUR_SUBSCRIPTION_ID"

# For managed identity support (recommended):
az identity create --name "azure-sentinel-lite-identity" \
  --resource-group "your-resource-group"
```

### 4. Enhanced Security Setup (Production)

```bash
# Generate secure API keys
openssl rand -hex 32  # For API_SECRET_KEY
openssl rand -hex 32  # For WEBHOOK_SECRET

# Store secrets securely in environment variables
export API_SECRET_KEY="$(openssl rand -hex 32)"
export WEBHOOK_SECRET="$(openssl rand -hex 32)"
```

### 5. Run Detection (Enhanced)

```bash
# Start secure web dashboard (recommended)
python azure_sentinel_lite.py --web
# Access at: http://localhost:5000
# Features: Rate limiting, API authentication, real-time updates

# Run all detectors with enhanced error handling
python azure_sentinel_lite.py

# Run specific detector with improved validation
python azure_sentinel_lite.py --detector port_scan

# Continuous monitoring with circuit breaker protection
python azure_sentinel_lite.py --monitor --interval 5

# Generate enhanced sample data and run detection
python azure_sentinel_lite.py --simulate

# View recent alerts with memory management
python azure_sentinel_lite.py --recent 10
```

## Usage Examples

### 🌐 Secure Web Dashboard (Recommended)
```bash
# Start the enhanced secure web dashboard
python azure_sentinel_lite.py --web

# Or run directly with security features
python web_dashboard.py
```

**Enhanced Features:**
- **Real-time Dashboard** with live statistics and secure WebSocket updates
- **Authenticated APIs** - Secure endpoints with API key authentication
- **Rate-Limited Access** - Protection against DoS attacks (10 req/min)
- **Interactive Controls** - Run detectors with one click (authenticated)
- **Visual Analytics** - Severity distribution, alert timelines, and type breakdowns
- **Live Monitoring** - Start/stop continuous monitoring with circuit breaker protection
- **Professional Interface** - Modern, responsive design with security headers
- **Memory Management** - Efficient data loading with pagination limits

**Security Headers Included:**
- Content Security Policy (CSP)
- X-Frame-Options protection
- Rate limiting with IP-based tracking
- Input validation and sanitization

Access the dashboard at: **http://localhost:5000**

**API Authentication:**
```bash
# For API calls, include the API key header:
curl -H "X-API-Key: your-api-key" \
  -X POST http://localhost:5000/api/run_detection \
  -H "Content-Type: application/json" \
  -d '{"detector": "port_scan"}'
```

### Basic Detection Scan (CLI)
```bash
python azure_sentinel_lite.py
```

### Continuous Monitoring
```bash
# Monitor every 5 minutes (default)
python azure_sentinel_lite.py --monitor

# Monitor every 10 minutes
python azure_sentinel_lite.py --monitor --interval 10
```

### Specific Detector
```bash
# Check for port scans only
python azure_sentinel_lite.py --detector port_scan

# Check for exposed VMs only
python azure_sentinel_lite.py --detector exposed_vm
```

### View Recent Alerts
```bash
# Show last 10 alerts
python azure_sentinel_lite.py --recent 10
```

## Detection Details

### 🌐 Port Scan Detection
- Monitors network traffic for scanning patterns
- Detects multiple port probes from single source
- Identifies reconnaissance activities
- **Triggers**: 5+ different ports or 10+ scan attempts

### 🖥️ Exposed VM Detection
- Scans for VMs with public IP addresses
- Checks Network Security Group (NSG) rules
- Identifies risky port exposures (SSH, RDP, databases)
- **Triggers**: Public IP + risky NSG rules

### 🌍 Unusual Sign-in Location
- Tracks user login locations using IP geolocation
- Detects impossible travel scenarios
- Identifies logins from high-risk countries
- **Triggers**: >500km from usual location or impossible travel time

### 👑 Elevated Activity Monitoring
- Monitors privileged user actions
- Tracks after-hours administrative activity
- Detects high-volume privilege operations
- **Triggers**: Rapid role changes, after-hours activity, high volume

### ⬆️ Privilege Escalation Detection
- Monitors Azure RBAC role assignments
- Tracks permission increases
- Risk-scores role changes
- **Triggers**: Escalation to Owner, Global Admin, or high-risk roles

## Email Alerts

When threats are detected, the system automatically sends email alerts containing:

- **Alert Type** and severity level
- **Detailed Information** about the threat
- **Affected Resources** and users
- **Recommended Actions** for remediation
- **Timestamp** and context

### Email Recipients
- **Admin Email**: Always receives all alerts
- **User Email**: Receives alerts related to their account (when available)

## Log Files

The system maintains structured logs:

- `logs/events_log.jsonl` - Raw security events
- `logs/alerts.jsonl` - Generated security alerts

## Configuration Options

### Detection Thresholds
```env
ALERT_THRESHOLD_MINUTES=60          # Alert aggregation window
MAX_FAILED_LOGINS=5                 # Failed login threshold
UNUSUAL_LOCATION_THRESHOLD_KM=500   # Distance threshold for unusual locations
```

### Email Settings
```env
SMTP_SERVER=smtp.gmail.com          # SMTP server
SMTP_PORT=587                       # SMTP port
EMAIL_USERNAME=<EMAIL> # Sender email
EMAIL_PASSWORD=your_app_password    # App password (not regular password)
ADMIN_EMAIL=<EMAIL>       # Admin notification email
```

## Testing

### Generate Sample Data
```bash
# Generate sample threat events
python simulator/simulate_threats.py

# Run detection on sample data
python azure_sentinel_lite.py --simulate
```

### Test Individual Detectors
```bash
# Test each detector individually
python detectors/detect_port_scan.py
python detectors/detect_exposed_vm.py
python detectors/detect_unusual_signin.py
python detectors/detect_elevated_activity.py
python simulator/detections/detect_role_escalation.py
```

## Troubleshooting

### Common Issues

1. **Azure Authentication Errors**
   - Verify service principal credentials
   - Check subscription ID and tenant ID
   - Ensure proper RBAC permissions

2. **Email Delivery Issues**
   - Use app passwords for Gmail (not regular password)
   - Check SMTP server and port settings
   - Verify firewall allows SMTP traffic

3. **No Alerts Generated**
   - Run with `--simulate` to generate test data
   - Check log files for events
   - Verify detection thresholds in config

### Debug Mode
```bash
# Enable verbose logging
export LOG_LEVEL=DEBUG
python azure_sentinel_lite.py
```

## 🔒 Enhanced Security Features

### **Built-in Security Protections**

#### **API Security**
- **Authentication Required**: All sensitive endpoints require API key authentication
- **Rate Limiting**: 10 requests per minute per IP address to prevent DoS attacks
- **Input Validation**: JSON schema validation with size limits and content filtering
- **HTTPS Enforcement**: Secure communication protocols (configure reverse proxy for production)

#### **File System Security**
- **Path Traversal Protection**: Prevents `../` directory traversal attacks
- **Filename Sanitization**: Removes dangerous characters from file names
- **Extension Validation**: Only allows safe file extensions (.log, .json, .txt, etc.)
- **Directory Restriction**: Confines all operations to application directory

#### **Network Security**
- **Retry Logic**: Exponential backoff with jitter for network operations
- **Timeout Protection**: Configurable timeouts prevent hanging connections
- **SSRF Prevention**: Blocks requests to private IP ranges and localhost
- **Content Length Limits**: Prevents memory exhaustion from large responses

#### **Process Security**
- **No Shell Execution**: Direct binary calls only, prevents command injection
- **Path Validation**: Validates all executable paths before subprocess execution
- **Environment Isolation**: Clean environment variables for subprocess calls
- **Timeout Enforcement**: 5-minute maximum execution time for subprocesses

#### **Memory Management**
- **Pagination Limits**: Maximum 1000 alerts loaded at once
- **File Size Limits**: Automatic log rotation when files exceed 100MB
- **Thread-Safe Operations**: File locking prevents data corruption
- **Circuit Breaker Pattern**: Automatic failure detection and recovery

### **Azure Security Integration**

#### **Managed Identity Support**
```bash
# Preferred authentication method (no stored credentials)
# Automatically detects and uses managed identity when available
# Falls back to service principal if managed identity unavailable
```

#### **Secure Configuration Management**
```bash
# Store sensitive configuration in environment variables
export API_SECRET_KEY="your-secure-api-key"
export WEBHOOK_SECRET="your-webhook-secret"

# Application reads from environment variables
# Supports .env file for development
```

#### **Azure Logic Apps Security**
```bash
# Webhook signature validation using HMAC-SHA256
# Common Alert Schema support for enterprise integration
# Automatic retry logic with exponential backoff
```

### **Production Security Checklist**

- ✅ **Use Managed Identity** instead of service principal credentials
- ✅ **Store secrets securely** in environment variables or secure configuration
- ✅ **Generate strong API keys** using `openssl rand -hex 32`
- ✅ **Configure webhook signatures** for Logic Apps integration
- ✅ **Enable HTTPS** using reverse proxy (nginx/Apache)
- ✅ **Set up monitoring** for the monitoring system itself
- ✅ **Implement log aggregation** for centralized security monitoring
- ✅ **Regular credential rotation** and access reviews
- ✅ **Network segmentation** and firewall rules
- ✅ **Backup and disaster recovery** procedures

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new detectors
4. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**⚠️ Important**: This is a security monitoring tool. Ensure you have proper authorization before deploying in any environment.
