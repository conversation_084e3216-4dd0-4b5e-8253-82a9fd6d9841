import smtplib
import json
import re
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from datetime import datetime, timezone
from typing import List, Dict
from .config import Config
from .network_utils import SafeSMTPClient, NetworkError

class EmailNotifier:
    """Email notification system for security alerts"""

    def __init__(self):
        self.smtp_server = Config.SMTP_SERVER
        self.smtp_port = Config.SMTP_PORT
        self.username = Config.EMAIL_USERNAME
        self.password = Config.EMAIL_PASSWORD
        self.admin_email = Config.ADMIN_EMAIL

        # Initialize safe SMTP client
        if all([self.smtp_server, self.smtp_port, self.username, self.password]):
            try:
                self.safe_smtp = SafeSMTPClient(
                    smtp_server=self.smtp_server,
                    smtp_port=self.smtp_port,
                    username=self.username,
                    password=self.password
                )
            except Exception as e:
                print(f"⚠️ Failed to initialize safe SMTP client: {e}")
                self.safe_smtp = None
        else:
            self.safe_smtp = None

    def send_alert(self, alert):
        """Send email alert"""
        # Check if email is configured with real credentials
        if (not all([self.smtp_server, self.username, self.password, self.admin_email]) or
            self.username in ['<EMAIL>', '<EMAIL>'] or
            self.password in ['your_app_password', 'demo_password']):
            print("📧 Email not configured - showing alert details:")
            print(f"   To: {self.admin_email or '<EMAIL>'}")
            print(f"   Subject: {self._get_alert_subject(alert)}")
            print(f"   Severity: {alert.get('severity', 'UNKNOWN')}")
            print(f"   Message: {alert.get('message', 'No message')}")
            return True

        try:
            # Extract alert data
            alert_type = alert.get('type', 'security_alert')
            user_email = alert.get('user')  # Get affected user's email if available

            # Recipients: admin + affected user (if available)
            recipients = [self.admin_email]
            if user_email and user_email != self.admin_email:
                # Validate email format with regex
                if self._is_valid_email(user_email):
                    recipients.append(user_email)

            # Create email body with full alert data
            body = self._create_alert_body(alert_type, alert)
            subject = self._get_alert_subject(alert)

            # Use safe SMTP client if available
            if self.safe_smtp:
                try:
                    success = self.safe_smtp.send_email(
                        to_addresses=recipients,
                        subject=subject,
                        body=body,
                        from_address=self.username
                    )
                    if success:
                        print(f"✅ Alert email sent for {alert_type}")
                        return True
                except NetworkError as e:
                    print(f"❌ Network error sending email: {e}")
                    return False
            else:
                # Fallback to basic SMTP
                print("⚠️ Using fallback SMTP (safe client not available)")
                return self._send_basic_email(recipients, subject, body, alert_type)

        except Exception as e:
            print(f"❌ Failed to send email alert: {e}")
            return False

    def _send_basic_email(self, recipients: list, subject: str, body: str, alert_type: str) -> bool:
        """Fallback basic email sending"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['Subject'] = subject
            msg['To'] = ', '.join(recipients)
            msg.attach(MIMEText(body, 'html'))

            with smtplib.SMTP(self.smtp_server, self.smtp_port, timeout=30) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)

            print(f"✅ Alert email sent for {alert_type} (basic SMTP)")
            return True

        except Exception as e:
            print(f"❌ Basic SMTP failed: {e}")
            return False

    def _is_valid_email(self, email: str) -> bool:
        """Validate email format using regex"""
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(email_pattern, email) is not None

    def _create_alert_body(self, alert_type: str, alert_data: Dict) -> str:
        """Create HTML email body for alert"""
        timestamp = alert_data.get('timestamp', datetime.now(timezone.utc).isoformat())

        html_body = f"""
        <html>
        <body>
            <h2 style="color: #d73027;">🚨 Azure Security Alert</h2>
            <h3>Alert Type: {alert_type}</h3>
            <p><strong>Timestamp:</strong> {timestamp}</p>

            <h4>Alert Details:</h4>
            <table border="1" style="border-collapse: collapse; width: 100%;">
        """

        # Add alert-specific details
        for key, value in alert_data.items():
            if key != 'timestamp':
                html_body += f"<tr><td><strong>{key.replace('_', ' ').title()}</strong></td><td>{value}</td></tr>"

        html_body += """
            </table>

            <h4>Recommended Actions:</h4>
            <ul>
        """

        # Add recommendations based on alert type
        recommendations = self._get_recommendations(alert_type)
        for rec in recommendations:
            html_body += f"<li>{rec}</li>"

        html_body += """
            </ul>

            <p style="color: #666; font-size: 12px;">
                This alert was generated by Azure Sentinel Lite.<br>
                Please investigate this activity immediately.
            </p>
        </body>
        </html>
        """

        return html_body

    def _get_recommendations(self, alert_type: str) -> List[str]:
        """Get recommendations based on alert type"""
        recommendations = {
            'privilege_escalation': [
                'Verify if the role change was authorized',
                'Check with the user if they requested this change',
                'Review recent activity for this user account',
                'Consider temporarily disabling the account if suspicious'
            ],
            'port_scan': [
                'Check network security group rules',
                'Verify if the source IP is legitimate',
                'Consider blocking the source IP if malicious',
                'Review firewall logs for additional activity'
            ],
            'exposed_vm': [
                'Review if public IP exposure is necessary',
                'Implement network security group restrictions',
                'Consider using Azure Bastion for secure access',
                'Audit VM security configuration'
            ],
            'unusual_signin': [
                'Verify with the user if they traveled to this location',
                'Check for concurrent sessions from different locations',
                'Consider requiring MFA for this user',
                'Monitor for additional suspicious activity'
            ],
            'elevated_activity': [
                'Verify if the elevated actions were authorized',
                'Review the business justification for these actions',
                'Check if proper approval process was followed',
                'Monitor for additional privileged operations'
            ]
        }

        return recommendations.get(alert_type, ['Investigate this activity immediately'])

    def _get_alert_subject(self, alert):
        """Generate email subject line"""
        severity = alert.get('severity', 'UNKNOWN')
        alert_type = alert.get('type', 'security_alert').replace('_', ' ').title()
        return f"[{severity}] Azure Security Alert - {alert_type}"
