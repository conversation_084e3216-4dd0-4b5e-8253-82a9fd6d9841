"""
Safe file operations with proper error handling and locking
"""
import json
import fcntl
import time
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from contextlib import contextmanager
import logging
import shutil
from datetime import datetime, timedelta
import threading

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileOperationError(Exception):
    """Custom exception for file operation errors"""
    pass

_file_locks = {}
_lock_lock = threading.Lock()

def get_file_lock(filepath: str) -> threading.Lock:
    """Get a thread lock for a specific file"""
    with _lock_lock:
        if filepath not in _file_locks:
            _file_locks[filepath] = threading.Lock()
        return _file_locks[filepath]

@contextmanager
def file_lock(file_path: Path, mode: str = 'r', timeout: int = 10):
    """
    Context manager for file locking with timeout
    """
    file_obj = None
    try:
        file_obj = open(file_path, mode)

        # Try to acquire lock with timeout
        start_time = time.time()
        while True:
            try:
                fcntl.flock(file_obj.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                break
            except IOError:
                if time.time() - start_time > timeout:
                    raise FileOperationError(f"Could not acquire lock on {file_path} within {timeout} seconds")
                time.sleep(0.1)

        yield file_obj

    except Exception as e:
        logger.error(f"File operation error on {file_path}: {e}")
        raise FileOperationError(f"File operation failed: {e}")
    finally:
        if file_obj:
            try:
                fcntl.flock(file_obj.fileno(), fcntl.LOCK_UN)
                file_obj.close()
            except Exception as e:
                logger.error(f"Error releasing lock on {file_path}: {e}")

def safe_read_jsonl(file_path: Path, max_lines: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    Safely read JSONL file with error handling and memory limits
    """
    results = []
    corrupted_lines = 0

    try:
        with get_file_lock(str(file_path)):
            if not file_path.exists():
                logger.warning(f"File does not exist: {file_path}")
                return results

            with open(file_path, 'r') as f:
                for line_num, line in enumerate(f, 1):
                    # Memory protection - limit number of lines
                    if max_lines and len(results) >= max_lines:
                        logger.warning(f"Reached maximum lines limit ({max_lines}) for {file_path}")
                        break

                    line = line.strip()
                    if not line:
                        continue

                    try:
                        json_data = json.loads(line)
                        results.append(json_data)
                    except json.JSONDecodeError as e:
                        corrupted_lines += 1
                        logger.warning(f"Corrupted JSON on line {line_num} in {file_path}: {e}")

                        # If too many corrupted lines, stop processing
                        if corrupted_lines > 100:
                            logger.error(f"Too many corrupted lines in {file_path}, stopping read")
                            break

    except FileOperationError:
        raise
    except Exception as e:
        logger.error(f"Unexpected error reading {file_path}: {e}")
        raise FileOperationError(f"Failed to read file: {e}")

    if corrupted_lines > 0:
        logger.warning(f"Found {corrupted_lines} corrupted lines in {file_path}")

    return results

def safe_write_jsonl(file_path: Path, data, mode='a') -> bool:
    """
    Safely write to JSONL file with size limits and error handling
    Accepts either a single dict or a list of dicts
    """
    try:
        with get_file_lock(str(file_path)):
            with open(file_path, mode) as f:
                # Handle both single dict and list of dicts
                if isinstance(data, dict):
                    json_str = json.dumps(data, ensure_ascii=False)
                    f.write(json_str + '\n')
                    f.flush()
                    os.fsync(f.fileno())
                elif isinstance(data, list):
                    for item in data:
                        json_str = json.dumps(item, ensure_ascii=False)
                        f.write(json_str + '\n')
                        f.flush()
                        os.fsync(f.fileno())
                else:
                    raise ValueError(f"Data must be dict or list of dicts, got {type(data)}")

        return True

    except FileOperationError:
        raise
    except Exception as e:
        logger.error(f"Failed to write to {file_path}: {e}")
        return False

def clear_file(filepath: Path) -> bool:
    """Clear contents of a file while preserving it"""
    try:
        with get_file_lock(str(filepath)):
            filepath.write_text('')
        return True
    except Exception as e:
        logger.error(f"Error clearing {filepath}: {e}")
        return False

def rotate_log_file(file_path: Path, max_backups: int = 5) -> bool:
    """
    Rotate log file when it gets too large
    """
    try:
        if not file_path.exists():
            return True

        # Move existing backups
        for i in range(max_backups - 1, 0, -1):
            old_backup = file_path.with_suffix(f'{file_path.suffix}.{i}')
            new_backup = file_path.with_suffix(f'{file_path.suffix}.{i + 1}')

            if old_backup.exists():
                if new_backup.exists():
                    new_backup.unlink()
                old_backup.rename(new_backup)

        # Move current file to .1
        backup_file = file_path.with_suffix(f'{file_path.suffix}.1')
        if backup_file.exists():
            backup_file.unlink()
        file_path.rename(backup_file)

        logger.info(f"Rotated log file: {file_path}")
        return True

    except Exception as e:
        logger.error(f"Failed to rotate log file {file_path}: {e}")
        return False

def archive_old_logs(log_dir: Path, archive_dir: Path, days: int = 30) -> bool:
    """Archive log files older than specified days"""
    try:
        # Create archive directory
        archive_dir.mkdir(parents=True, exist_ok=True)

        # Get current time
        now = datetime.now()

        # Track success
        all_success = True

        # Process each log file
        for log_file in log_dir.glob('*.jsonl'):
            try:
                with get_file_lock(str(log_file)):
                    # Check file age
                    file_time = datetime.fromtimestamp(log_file.stat().st_mtime)

                    if now - file_time > timedelta(days=days):
                        # Create archive filename with timestamp
                        archive_name = f"{log_file.stem}_{file_time.strftime('%Y%m%d')}{log_file.suffix}"
                        archive_path = archive_dir / archive_name

                        # Move file to archive
                        shutil.move(str(log_file), str(archive_path))

                        # Create new empty log file
                        log_file.touch()
            except Exception as e:
                logger.error(f"Error archiving {log_file}: {e}")
                all_success = False

        return all_success
    except Exception as e:
        logger.error(f"Error in archive operation: {e}")
        return False

def cleanup_archives(archive_dir: Path, max_age_days: int = 90) -> bool:
    """Remove archives older than specified days"""
    try:
        if not archive_dir.exists():
            return True

        # Get current time
        now = datetime.now()

        # Track success
        all_success = True

        # Process each archive file
        for archive_file in archive_dir.glob('*.jsonl'):
            try:
                # Check file age
                file_time = datetime.fromtimestamp(archive_file.stat().st_mtime)

                if now - file_time > timedelta(days=max_age_days):
                    archive_file.unlink()
            except Exception as e:
                logger.error(f"Error cleaning up {archive_file}: {e}")
                all_success = False

        return all_success
    except Exception as e:
        logger.error(f"Error in cleanup operation: {e}")
        return False

def safe_read_recent_lines(file_path: Path, num_lines: int = 10) -> List[Dict[str, Any]]:
    """
    Safely read the last N lines from a JSONL file
    """
    if not file_path.exists():
        return []

    try:
        # For small files, read all and return last N
        all_data = safe_read_jsonl(file_path, max_lines=1000)
        return all_data[-num_lines:] if len(all_data) >= num_lines else all_data

    except Exception as e:
        logger.error(f"Failed to read recent lines from {file_path}: {e}")
        return []

def validate_json_schema(data: Dict[str, Any], required_fields: List[str]) -> bool:
    """
    Validate JSON data against required schema
    """
    try:
        for field in required_fields:
            if field not in data:
                logger.warning(f"Missing required field: {field}")
                return False
        return True
    except Exception as e:
        logger.error(f"Schema validation error: {e}")
        return False

def cleanup_old_files(directory: Path, max_age_days: int = 30) -> int:
    """
    Clean up old log files to prevent disk space issues
    """
    if not directory.exists():
        return 0

    cleaned_count = 0
    cutoff_time = time.time() - (max_age_days * 24 * 3600)

    try:
        for file_path in directory.iterdir():
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                try:
                    file_path.unlink()
                    cleaned_count += 1
                    logger.info(f"Cleaned up old file: {file_path}")
                except Exception as e:
                    logger.error(f"Failed to delete old file {file_path}: {e}")

    except Exception as e:
        logger.error(f"Error during cleanup of {directory}: {e}")

    return cleaned_count

def get_file_stats(file_path: Path) -> Dict[str, Any]:
    """
    Get file statistics safely
    """
    try:
        if not file_path.exists():
            return {'exists': False}

        stat = file_path.stat()
        return {
            'exists': True,
            'size_bytes': stat.st_size,
            'size_mb': stat.st_size / (1024 * 1024),
            'modified_time': stat.st_mtime,
            'is_readable': os.access(file_path, os.R_OK),
            'is_writable': os.access(file_path, os.W_OK)
        }
    except Exception as e:
        logger.error(f"Failed to get stats for {file_path}: {e}")
        return {'exists': False, 'error': str(e)}
