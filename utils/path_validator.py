"""
Path validation utilities to prevent path traversal attacks
"""
import os
from pathlib import Path
from typing import Union, Optional
import logging

logger = logging.getLogger(__name__)

class PathValidationError(Exception):
    """Custom exception for path validation errors"""
    pass

class PathValidator:
    """Path validator to prevent directory traversal and other path-based attacks"""
    
    def __init__(self, base_directory: Optional[Union[str, Path]] = None):
        """
        Initialize path validator
        
        Args:
            base_directory: Base directory to restrict paths to (default: current working directory)
        """
        if base_directory is None:
            self.base_directory = Path.cwd()
        else:
            self.base_directory = Path(base_directory).resolve()
        
        # Ensure base directory exists
        self.base_directory.mkdir(parents=True, exist_ok=True)
        
        # Dangerous path patterns
        self.dangerous_patterns = [
            '..',           # Parent directory
            '~',            # Home directory
            '/etc/',        # System config
            '/var/',        # System var
            '/usr/',        # System usr
            '/bin/',        # System binaries
            '/sbin/',       # System binaries
            '/root/',       # Root home
            '/home/',       # User homes (unless specifically allowed)
            'C:\\',         # Windows system drive
            'D:\\',         # Windows drives
            '\\\\',         # UNC paths
            '$',            # Environment variables
        ]
        
        # Allowed file extensions
        self.allowed_extensions = {
            '.log', '.txt', '.json', '.csv', '.xml', '.yaml', '.yml',
            '.md', '.rst', '.conf', '.cfg', '.ini', '.properties'
        }
        
        # Maximum path length
        self.max_path_length = 260  # Windows MAX_PATH limit
        
        # Maximum filename length
        self.max_filename_length = 255  # Most filesystems
    
    def validate_path(self, path: Union[str, Path], 
                     allow_creation: bool = False,
                     require_extension: bool = True) -> Path:
        """
        Validate and sanitize a file path
        
        Args:
            path: Path to validate
            allow_creation: Whether to allow creation of non-existent paths
            require_extension: Whether to require a valid file extension
        
        Returns:
            Validated and resolved Path object
        
        Raises:
            PathValidationError: If path is invalid or dangerous
        """
        try:
            # Convert to Path object
            if isinstance(path, str):
                path_obj = Path(path)
            else:
                path_obj = path
            
            # Basic validation
            self._validate_basic_path(path_obj)
            
            # Resolve path (this will resolve .. and . components)
            try:
                resolved_path = path_obj.resolve()
            except (OSError, ValueError) as e:
                raise PathValidationError(f"Cannot resolve path: {e}")
            
            # Check if path is within base directory
            self._validate_within_base(resolved_path)
            
            # Check for dangerous patterns
            self._validate_no_dangerous_patterns(resolved_path)
            
            # Validate filename
            self._validate_filename(resolved_path.name)
            
            # Check file extension if required
            if require_extension:
                self._validate_extension(resolved_path)
            
            # Check if path exists or can be created
            if not allow_creation and not resolved_path.exists():
                # For non-existent paths, check if parent exists
                if not resolved_path.parent.exists():
                    raise PathValidationError(f"Parent directory does not exist: {resolved_path.parent}")
            
            return resolved_path
            
        except PathValidationError:
            raise
        except Exception as e:
            raise PathValidationError(f"Path validation failed: {e}")
    
    def _validate_basic_path(self, path: Path) -> None:
        """Basic path validation"""
        path_str = str(path)
        
        # Check path length
        if len(path_str) > self.max_path_length:
            raise PathValidationError(f"Path too long: {len(path_str)} > {self.max_path_length}")
        
        # Check for null bytes
        if '\x00' in path_str:
            raise PathValidationError("Path contains null bytes")
        
        # Check for control characters
        if any(ord(c) < 32 for c in path_str if c not in '\t\n\r'):
            raise PathValidationError("Path contains control characters")
        
        # Check for empty path
        if not path_str.strip():
            raise PathValidationError("Path is empty")
    
    def _validate_within_base(self, resolved_path: Path) -> None:
        """Ensure path is within base directory"""
        try:
            resolved_path.relative_to(self.base_directory)
        except ValueError:
            raise PathValidationError(f"Path outside base directory: {resolved_path} not in {self.base_directory}")
    
    def _validate_no_dangerous_patterns(self, path: Path) -> None:
        """Check for dangerous path patterns"""
        path_str = str(path).lower()
        
        for pattern in self.dangerous_patterns:
            if pattern.lower() in path_str:
                # Special handling for .. - check if it's actually resolved
                if pattern == '..' and '..' not in str(path):
                    continue
                raise PathValidationError(f"Dangerous path pattern detected: {pattern}")
        
        # Check for suspicious characters
        suspicious_chars = ['<', '>', '|', '*', '?', '"']
        for char in suspicious_chars:
            if char in str(path):
                raise PathValidationError(f"Suspicious character in path: {char}")
    
    def _validate_filename(self, filename: str) -> None:
        """Validate filename"""
        if not filename:
            raise PathValidationError("Filename is empty")
        
        if len(filename) > self.max_filename_length:
            raise PathValidationError(f"Filename too long: {len(filename)} > {self.max_filename_length}")
        
        # Check for reserved names (Windows)
        reserved_names = {
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        }
        
        name_without_ext = filename.split('.')[0].upper()
        if name_without_ext in reserved_names:
            raise PathValidationError(f"Reserved filename: {filename}")
        
        # Check for leading/trailing spaces or dots
        if filename.startswith((' ', '.')) or filename.endswith((' ', '.')):
            raise PathValidationError(f"Invalid filename format: {filename}")
    
    def _validate_extension(self, path: Path) -> None:
        """Validate file extension"""
        extension = path.suffix.lower()
        
        if not extension:
            raise PathValidationError("File has no extension")
        
        if extension not in self.allowed_extensions:
            raise PathValidationError(f"File extension not allowed: {extension}")
    
    def sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename by removing/replacing dangerous characters
        
        Args:
            filename: Original filename
        
        Returns:
            Sanitized filename
        """
        import re
        
        # Remove/replace dangerous characters
        sanitized = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '_', filename)
        
        # Remove leading/trailing spaces and dots
        sanitized = sanitized.strip(' .')
        
        # Ensure it's not empty
        if not sanitized:
            sanitized = 'file'
        
        # Truncate if too long
        if len(sanitized) > self.max_filename_length:
            name, ext = os.path.splitext(sanitized)
            max_name_length = self.max_filename_length - len(ext)
            sanitized = name[:max_name_length] + ext
        
        return sanitized
    
    def create_safe_path(self, *path_parts: str, 
                        sanitize: bool = True,
                        ensure_extension: Optional[str] = None) -> Path:
        """
        Create a safe path from components
        
        Args:
            path_parts: Path components
            sanitize: Whether to sanitize filename components
            ensure_extension: Ensure path has this extension
        
        Returns:
            Safe path within base directory
        """
        # Sanitize path parts if requested
        if sanitize:
            safe_parts = []
            for part in path_parts:
                if part:  # Skip empty parts
                    safe_parts.append(self.sanitize_filename(part))
            path_parts = safe_parts
        
        # Create path
        safe_path = self.base_directory
        for part in path_parts:
            safe_path = safe_path / part
        
        # Ensure extension if specified
        if ensure_extension and not safe_path.suffix:
            if not ensure_extension.startswith('.'):
                ensure_extension = '.' + ensure_extension
            safe_path = safe_path.with_suffix(ensure_extension)
        
        # Validate the final path
        return self.validate_path(safe_path, allow_creation=True, require_extension=bool(ensure_extension))
    
    def is_safe_path(self, path: Union[str, Path]) -> bool:
        """
        Check if path is safe without raising exceptions
        
        Args:
            path: Path to check
        
        Returns:
            True if path is safe
        """
        try:
            self.validate_path(path, allow_creation=True, require_extension=False)
            return True
        except PathValidationError:
            return False
        except Exception:
            return False

# Global path validator for the application
app_path_validator = PathValidator()

# Convenience functions
def validate_path(path: Union[str, Path], **kwargs) -> Path:
    """Validate a path using the global validator"""
    return app_path_validator.validate_path(path, **kwargs)

def sanitize_filename(filename: str) -> str:
    """Sanitize a filename using the global validator"""
    return app_path_validator.sanitize_filename(filename)

def create_safe_path(*path_parts: str, **kwargs) -> Path:
    """Create a safe path using the global validator"""
    return app_path_validator.create_safe_path(*path_parts, **kwargs)

def is_safe_path(path: Union[str, Path]) -> bool:
    """Check if path is safe using the global validator"""
    return app_path_validator.is_safe_path(path)
