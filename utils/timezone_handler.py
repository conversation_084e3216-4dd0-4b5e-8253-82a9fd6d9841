"""
Timezone handling utilities for consistent datetime operations
"""
from datetime import datetime, timezone
import pytz
from typing import Optional, Union
import logging

logger = logging.getLogger(__name__)

class TimezoneHandler:
    """Centralized timezone handling for the application"""
    
    def __init__(self, default_timezone: str = 'UTC'):
        """
        Initialize timezone handler
        
        Args:
            default_timezone: Default timezone to use (default: UTC)
        """
        self.default_tz = pytz.timezone(default_timezone)
        self.utc = pytz.UTC
    
    def now_utc(self) -> datetime:
        """Get current time in UTC"""
        return datetime.now(self.utc)
    
    def now_local(self, timezone_name: Optional[str] = None) -> datetime:
        """Get current time in specified timezone or default"""
        tz = pytz.timezone(timezone_name) if timezone_name else self.default_tz
        return datetime.now(tz)
    
    def to_utc(self, dt: Union[datetime, str], source_timezone: Optional[str] = None) -> datetime:
        """
        Convert datetime to UTC
        
        Args:
            dt: Datetime object or ISO string
            source_timezone: Source timezone (if dt is naive)
        
        Returns:
            UTC datetime
        """
        try:
            # Handle string input
            if isinstance(dt, str):
                dt = self.parse_datetime_string(dt)
            
            # If datetime is naive, assume it's in source_timezone or default
            if dt.tzinfo is None:
                if source_timezone:
                    tz = pytz.timezone(source_timezone)
                else:
                    tz = self.default_tz
                dt = tz.localize(dt)
            
            # Convert to UTC
            return dt.astimezone(self.utc)
            
        except Exception as e:
            logger.error(f"Error converting datetime to UTC: {e}")
            return self.now_utc()
    
    def from_utc(self, utc_dt: datetime, target_timezone: str) -> datetime:
        """
        Convert UTC datetime to target timezone
        
        Args:
            utc_dt: UTC datetime
            target_timezone: Target timezone name
        
        Returns:
            Datetime in target timezone
        """
        try:
            if utc_dt.tzinfo is None:
                utc_dt = self.utc.localize(utc_dt)
            
            target_tz = pytz.timezone(target_timezone)
            return utc_dt.astimezone(target_tz)
            
        except Exception as e:
            logger.error(f"Error converting UTC to {target_timezone}: {e}")
            return utc_dt
    
    def parse_datetime_string(self, dt_string: str) -> datetime:
        """
        Parse various datetime string formats
        
        Args:
            dt_string: Datetime string in various formats
        
        Returns:
            Parsed datetime object
        """
        try:
            # Handle ISO format with Z suffix
            if dt_string.endswith('Z'):
                dt_string = dt_string[:-1] + '+00:00'
            
            # Try ISO format first
            try:
                return datetime.fromisoformat(dt_string)
            except ValueError:
                pass
            
            # Try common formats
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%S.%f',
                '%Y-%m-%d',
                '%m/%d/%Y %H:%M:%S',
                '%d/%m/%Y %H:%M:%S'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(dt_string, fmt)
                except ValueError:
                    continue
            
            # If all else fails, try dateutil parser
            try:
                from dateutil import parser
                return parser.parse(dt_string)
            except ImportError:
                logger.warning("dateutil not available for datetime parsing")
            except Exception:
                pass
            
            raise ValueError(f"Unable to parse datetime string: {dt_string}")
            
        except Exception as e:
            logger.error(f"Error parsing datetime string '{dt_string}': {e}")
            return self.now_utc()
    
    def format_datetime(self, dt: datetime, format_string: str = '%Y-%m-%d %H:%M:%S %Z') -> str:
        """
        Format datetime with timezone info
        
        Args:
            dt: Datetime object
            format_string: Format string
        
        Returns:
            Formatted datetime string
        """
        try:
            return dt.strftime(format_string)
        except Exception as e:
            logger.error(f"Error formatting datetime: {e}")
            return str(dt)
    
    def is_within_time_window(self, 
                             event_time: Union[datetime, str], 
                             window_minutes: int,
                             reference_time: Optional[datetime] = None) -> bool:
        """
        Check if event time is within specified time window
        
        Args:
            event_time: Event datetime or string
            window_minutes: Time window in minutes
            reference_time: Reference time (default: now)
        
        Returns:
            True if within window
        """
        try:
            if reference_time is None:
                reference_time = self.now_utc()
            
            # Convert both times to UTC
            event_utc = self.to_utc(event_time)
            ref_utc = self.to_utc(reference_time)
            
            # Calculate time difference
            time_diff = abs((ref_utc - event_utc).total_seconds() / 60)
            
            return time_diff <= window_minutes
            
        except Exception as e:
            logger.error(f"Error checking time window: {e}")
            return False
    
    def get_time_ago_string(self, dt: Union[datetime, str]) -> str:
        """
        Get human-readable time ago string
        
        Args:
            dt: Datetime object or string
        
        Returns:
            Human-readable time difference
        """
        try:
            event_utc = self.to_utc(dt)
            now_utc = self.now_utc()
            
            diff = now_utc - event_utc
            seconds = diff.total_seconds()
            
            if seconds < 60:
                return f"{int(seconds)} seconds ago"
            elif seconds < 3600:
                return f"{int(seconds / 60)} minutes ago"
            elif seconds < 86400:
                return f"{int(seconds / 3600)} hours ago"
            else:
                return f"{int(seconds / 86400)} days ago"
                
        except Exception as e:
            logger.error(f"Error calculating time ago: {e}")
            return "unknown time ago"
    
    def validate_timezone(self, timezone_name: str) -> bool:
        """
        Validate if timezone name is valid
        
        Args:
            timezone_name: Timezone name to validate
        
        Returns:
            True if valid timezone
        """
        try:
            pytz.timezone(timezone_name)
            return True
        except pytz.exceptions.UnknownTimeZoneError:
            return False

# Global timezone handler instance
tz_handler = TimezoneHandler()

# Convenience functions
def now_utc() -> datetime:
    """Get current UTC time"""
    return tz_handler.now_utc()

def to_utc(dt: Union[datetime, str], source_timezone: Optional[str] = None) -> datetime:
    """Convert datetime to UTC"""
    return tz_handler.to_utc(dt, source_timezone)

def parse_datetime(dt_string: str) -> datetime:
    """Parse datetime string"""
    return tz_handler.parse_datetime_string(dt_string)

def is_within_window(event_time: Union[datetime, str], window_minutes: int) -> bool:
    """Check if event is within time window"""
    return tz_handler.is_within_time_window(event_time, window_minutes)

def time_ago(dt: Union[datetime, str]) -> str:
    """Get time ago string"""
    return tz_handler.get_time_ago_string(dt)
