"""
JSON schema validation utilities for secure data processing
"""
import json
import re
from typing import Dict, Any, List, Optional, Union
import logging

logger = logging.getLogger(__name__)

class JSONValidationError(Exception):
    """Custom exception for JSON validation errors"""
    pass

class J<PERSON>NValidator:
    """JSON schema validator with security-focused validation"""
    
    # Common schemas for the application
    SCHEMAS = {
        'alert': {
            'type': 'object',
            'required': ['timestamp', 'type', 'severity'],
            'properties': {
                'timestamp': {'type': 'string', 'pattern': r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}'},
                'type': {'type': 'string', 'enum': ['port_scan', 'exposed_vm', 'unusual_signin', 'elevated_activity', 'privilege_escalation']},
                'severity': {'type': 'string', 'enum': ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']},
                'message': {'type': 'string', 'maxLength': 1000},
                'ip_address': {'type': 'string', 'pattern': r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$'},
                'user': {'type': 'string', 'maxLength': 100},
                'resource': {'type': 'string', 'maxLength': 200}
            },
            'additionalProperties': True,
            'maxProperties': 20
        },
        'event': {
            'type': 'object',
            'required': ['timestamp', 'type'],
            'properties': {
                'timestamp': {'type': 'string', 'pattern': r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}'},
                'type': {'type': 'string', 'enum': ['network_access', 'login', 'role_change', 'resource_access']},
                'ip_address': {'type': 'string', 'pattern': r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$'},
                'user': {'type': 'string', 'maxLength': 100},
                'port': {'type': 'integer', 'minimum': 1, 'maximum': 65535},
                'location': {'type': 'string', 'maxLength': 100}
            },
            'additionalProperties': True,
            'maxProperties': 15
        },
        'api_request': {
            'type': 'object',
            'properties': {
                'detector': {'type': 'string', 'enum': ['all', 'port_scan', 'exposed_vm', 'unusual_signin', 'elevated_activity', 'privilege_escalation']},
                'scenario_type': {'type': 'string', 'enum': ['port_scan', 'privilege_escalation', 'data_exfiltration']},
                'max_results': {'type': 'integer', 'minimum': 1, 'maximum': 1000}
            },
            'additionalProperties': False,
            'maxProperties': 10
        },
        'config': {
            'type': 'object',
            'properties': {
                'smtp_port': {'type': 'integer', 'minimum': 1, 'maximum': 65535},
                'cpu_threshold': {'type': 'number', 'minimum': 0, 'maximum': 100},
                'memory_threshold': {'type': 'number', 'minimum': 0, 'maximum': 100},
                'max_failed_logins': {'type': 'integer', 'minimum': 1, 'maximum': 100},
                'alert_threshold_minutes': {'type': 'integer', 'minimum': 1, 'maximum': 1440}
            },
            'additionalProperties': True
        }
    }
    
    def __init__(self):
        self.max_json_size = 1024 * 1024  # 1MB max JSON size
        self.max_string_length = 10000
        self.max_array_length = 1000
        self.max_object_depth = 10
    
    def validate_json_string(self, json_string: str, schema_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Validate JSON string with security checks
        
        Args:
            json_string: JSON string to validate
            schema_name: Schema name to validate against
        
        Returns:
            Parsed and validated JSON data
        
        Raises:
            JSONValidationError: If validation fails
        """
        # Size check
        if len(json_string) > self.max_json_size:
            raise JSONValidationError(f"JSON size exceeds limit: {len(json_string)} > {self.max_json_size}")
        
        # Parse JSON
        try:
            data = json.loads(json_string)
        except json.JSONDecodeError as e:
            raise JSONValidationError(f"Invalid JSON format: {e}")
        
        # Validate against schema if provided
        if schema_name:
            self.validate_data(data, schema_name)
        
        # Security validation
        self._security_validate(data)
        
        return data
    
    def validate_data(self, data: Any, schema_name: str) -> bool:
        """
        Validate data against a named schema
        
        Args:
            data: Data to validate
            schema_name: Schema name
        
        Returns:
            True if valid
        
        Raises:
            JSONValidationError: If validation fails
        """
        if schema_name not in self.SCHEMAS:
            raise JSONValidationError(f"Unknown schema: {schema_name}")
        
        schema = self.SCHEMAS[schema_name]
        return self._validate_against_schema(data, schema, schema_name)
    
    def _validate_against_schema(self, data: Any, schema: Dict[str, Any], context: str = "") -> bool:
        """
        Validate data against schema definition
        """
        try:
            # Type validation
            expected_type = schema.get('type')
            if expected_type:
                if not self._check_type(data, expected_type):
                    raise JSONValidationError(f"Type mismatch in {context}: expected {expected_type}, got {type(data).__name__}")
            
            # Object validation
            if expected_type == 'object' and isinstance(data, dict):
                self._validate_object(data, schema, context)
            
            # Array validation
            elif expected_type == 'array' and isinstance(data, list):
                self._validate_array(data, schema, context)
            
            # String validation
            elif expected_type == 'string' and isinstance(data, str):
                self._validate_string(data, schema, context)
            
            # Number validation
            elif expected_type in ['number', 'integer'] and isinstance(data, (int, float)):
                self._validate_number(data, schema, context)
            
            return True
            
        except Exception as e:
            if isinstance(e, JSONValidationError):
                raise
            raise JSONValidationError(f"Validation error in {context}: {e}")
    
    def _validate_object(self, data: Dict[str, Any], schema: Dict[str, Any], context: str):
        """Validate object against schema"""
        # Required fields
        required = schema.get('required', [])
        for field in required:
            if field not in data:
                raise JSONValidationError(f"Missing required field '{field}' in {context}")
        
        # Max properties
        max_props = schema.get('maxProperties')
        if max_props and len(data) > max_props:
            raise JSONValidationError(f"Too many properties in {context}: {len(data)} > {max_props}")
        
        # Additional properties
        properties = schema.get('properties', {})
        additional_allowed = schema.get('additionalProperties', True)
        
        for key, value in data.items():
            if key in properties:
                # Validate against property schema
                self._validate_against_schema(value, properties[key], f"{context}.{key}")
            elif not additional_allowed:
                raise JSONValidationError(f"Additional property '{key}' not allowed in {context}")
    
    def _validate_array(self, data: List[Any], schema: Dict[str, Any], context: str):
        """Validate array against schema"""
        max_items = schema.get('maxItems', self.max_array_length)
        if len(data) > max_items:
            raise JSONValidationError(f"Array too long in {context}: {len(data)} > {max_items}")
        
        # Validate items
        items_schema = schema.get('items')
        if items_schema:
            for i, item in enumerate(data):
                self._validate_against_schema(item, items_schema, f"{context}[{i}]")
    
    def _validate_string(self, data: str, schema: Dict[str, Any], context: str):
        """Validate string against schema"""
        # Length check
        max_length = schema.get('maxLength', self.max_string_length)
        if len(data) > max_length:
            raise JSONValidationError(f"String too long in {context}: {len(data)} > {max_length}")
        
        # Pattern check
        pattern = schema.get('pattern')
        if pattern and not re.match(pattern, data):
            raise JSONValidationError(f"String pattern mismatch in {context}: '{data}' does not match '{pattern}'")
        
        # Enum check
        enum_values = schema.get('enum')
        if enum_values and data not in enum_values:
            raise JSONValidationError(f"Invalid enum value in {context}: '{data}' not in {enum_values}")
    
    def _validate_number(self, data: Union[int, float], schema: Dict[str, Any], context: str):
        """Validate number against schema"""
        # Minimum check
        minimum = schema.get('minimum')
        if minimum is not None and data < minimum:
            raise JSONValidationError(f"Number too small in {context}: {data} < {minimum}")
        
        # Maximum check
        maximum = schema.get('maximum')
        if maximum is not None and data > maximum:
            raise JSONValidationError(f"Number too large in {context}: {data} > {maximum}")
    
    def _check_type(self, data: Any, expected_type: str) -> bool:
        """Check if data matches expected type"""
        type_map = {
            'string': str,
            'number': (int, float),
            'integer': int,
            'boolean': bool,
            'array': list,
            'object': dict,
            'null': type(None)
        }
        
        expected_python_type = type_map.get(expected_type)
        if expected_python_type is None:
            return False
        
        return isinstance(data, expected_python_type)
    
    def _security_validate(self, data: Any, depth: int = 0) -> None:
        """
        Perform security validation to prevent attacks
        """
        if depth > self.max_object_depth:
            raise JSONValidationError(f"Object depth exceeds limit: {depth} > {self.max_object_depth}")
        
        if isinstance(data, dict):
            if len(data) > 100:  # Reasonable limit for object properties
                raise JSONValidationError(f"Object has too many properties: {len(data)}")
            
            for key, value in data.items():
                # Check for suspicious keys
                if self._is_suspicious_key(key):
                    raise JSONValidationError(f"Suspicious key detected: {key}")
                
                self._security_validate(value, depth + 1)
        
        elif isinstance(data, list):
            if len(data) > self.max_array_length:
                raise JSONValidationError(f"Array too long: {len(data)} > {self.max_array_length}")
            
            for item in data:
                self._security_validate(item, depth + 1)
        
        elif isinstance(data, str):
            if len(data) > self.max_string_length:
                raise JSONValidationError(f"String too long: {len(data)} > {self.max_string_length}")
            
            # Check for suspicious content
            if self._contains_suspicious_content(data):
                raise JSONValidationError("Suspicious content detected in string")
    
    def _is_suspicious_key(self, key: str) -> bool:
        """Check if key contains suspicious patterns"""
        suspicious_patterns = [
            r'__.*__',  # Python dunder methods
            r'.*\.\.',  # Path traversal
            r'.*[<>].*',  # HTML/XML tags
            r'.*script.*',  # Script injection
            r'.*eval.*',  # Code evaluation
        ]
        
        for pattern in suspicious_patterns:
            if re.search(pattern, key, re.IGNORECASE):
                return True
        
        return False
    
    def _contains_suspicious_content(self, content: str) -> bool:
        """Check if string contains suspicious content"""
        suspicious_patterns = [
            r'<script.*?>.*?</script>',  # Script tags
            r'javascript:',  # JavaScript URLs
            r'data:.*base64',  # Base64 data URLs
            r'\\x[0-9a-fA-F]{2}',  # Hex encoded characters
            r'%[0-9a-fA-F]{2}',  # URL encoded characters (excessive)
        ]
        
        for pattern in suspicious_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        
        return False

# Global validator instance
json_validator = JSONValidator()

# Convenience functions
def validate_json(json_string: str, schema_name: Optional[str] = None) -> Dict[str, Any]:
    """Validate JSON string"""
    return json_validator.validate_json_string(json_string, schema_name)

def validate_data(data: Any, schema_name: str) -> bool:
    """Validate data against schema"""
    return json_validator.validate_data(data, schema_name)

def safe_json_loads(json_string: str, schema_name: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """Safely load JSON with validation"""
    try:
        return validate_json(json_string, schema_name)
    except JSONValidationError as e:
        logger.error(f"JSON validation failed: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error in JSON validation: {e}")
        return None
