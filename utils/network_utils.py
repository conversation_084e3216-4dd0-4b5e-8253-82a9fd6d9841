"""
Network utilities with retry logic and timeout handling
"""
import time
import socket
import smtplib
import requests
from typing import Optional, Callable, Any, Dict
from functools import wraps
import logging

logger = logging.getLogger(__name__)

class NetworkError(Exception):
    """Custom exception for network errors"""
    pass

class RetryConfig:
    """Configuration for retry logic"""
    def __init__(self, 
                 max_retries: int = 3,
                 base_delay: float = 1.0,
                 max_delay: float = 60.0,
                 exponential_base: float = 2.0,
                 jitter: bool = True):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter

def retry_on_network_error(retry_config: Optional[RetryConfig] = None):
    """
    Decorator for retrying network operations
    """
    if retry_config is None:
        retry_config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(retry_config.max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except (socket.error, socket.timeout, ConnectionError, 
                       requests.exceptions.RequestException, smtplib.SMTPException) as e:
                    last_exception = e
                    
                    if attempt == retry_config.max_retries:
                        logger.error(f"Network operation failed after {retry_config.max_retries} retries: {e}")
                        raise NetworkError(f"Network operation failed: {e}") from e
                    
                    # Calculate delay with exponential backoff
                    delay = min(
                        retry_config.base_delay * (retry_config.exponential_base ** attempt),
                        retry_config.max_delay
                    )
                    
                    # Add jitter to prevent thundering herd
                    if retry_config.jitter:
                        import random
                        delay *= (0.5 + random.random() * 0.5)
                    
                    logger.warning(f"Network operation failed (attempt {attempt + 1}/{retry_config.max_retries + 1}): {e}")
                    logger.info(f"Retrying in {delay:.2f} seconds...")
                    time.sleep(delay)
                
                except Exception as e:
                    # Non-network errors should not be retried
                    logger.error(f"Non-network error in network operation: {e}")
                    raise
            
            # This should never be reached, but just in case
            raise NetworkError(f"Network operation failed: {last_exception}")
        
        return wrapper
    return decorator

class SafeHTTPClient:
    """HTTP client with built-in retry logic and security features"""
    
    def __init__(self, 
                 timeout: int = 30,
                 retry_config: Optional[RetryConfig] = None,
                 max_response_size: int = 10 * 1024 * 1024):  # 10MB
        self.timeout = timeout
        self.retry_config = retry_config or RetryConfig()
        self.max_response_size = max_response_size
        
        # Create session with security headers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Azure-Sentinel-Lite/1.0',
            'Accept': 'application/json',
            'Connection': 'close'  # Prevent connection reuse issues
        })
    
    @retry_on_network_error()
    def post(self, url: str, data: Optional[Dict] = None, json_data: Optional[Dict] = None, 
             headers: Optional[Dict] = None, **kwargs) -> requests.Response:
        """
        Safe POST request with retry logic
        """
        # Validate URL
        if not self._is_safe_url(url):
            raise NetworkError(f"Unsafe URL detected: {url}")
        
        # Merge headers
        request_headers = self.session.headers.copy()
        if headers:
            request_headers.update(headers)
        
        # Make request with timeout
        response = self.session.post(
            url,
            data=data,
            json=json_data,
            headers=request_headers,
            timeout=self.timeout,
            stream=True,  # Stream to check content length
            **kwargs
        )
        
        # Check response size
        content_length = response.headers.get('content-length')
        if content_length and int(content_length) > self.max_response_size:
            response.close()
            raise NetworkError(f"Response too large: {content_length} > {self.max_response_size}")
        
        # Read response with size limit
        content = b''
        for chunk in response.iter_content(chunk_size=8192):
            content += chunk
            if len(content) > self.max_response_size:
                response.close()
                raise NetworkError(f"Response content too large: {len(content)} > {self.max_response_size}")
        
        # Replace response content
        response._content = content
        
        return response
    
    @retry_on_network_error()
    def get(self, url: str, headers: Optional[Dict] = None, **kwargs) -> requests.Response:
        """
        Safe GET request with retry logic
        """
        # Validate URL
        if not self._is_safe_url(url):
            raise NetworkError(f"Unsafe URL detected: {url}")
        
        # Merge headers
        request_headers = self.session.headers.copy()
        if headers:
            request_headers.update(headers)
        
        # Make request with timeout
        response = self.session.get(
            url,
            headers=request_headers,
            timeout=self.timeout,
            stream=True,
            **kwargs
        )
        
        # Check and limit response size (same as POST)
        content_length = response.headers.get('content-length')
        if content_length and int(content_length) > self.max_response_size:
            response.close()
            raise NetworkError(f"Response too large: {content_length} > {self.max_response_size}")
        
        content = b''
        for chunk in response.iter_content(chunk_size=8192):
            content += chunk
            if len(content) > self.max_response_size:
                response.close()
                raise NetworkError(f"Response content too large: {len(content)} > {self.max_response_size}")
        
        response._content = content
        return response
    
    def _is_safe_url(self, url: str) -> bool:
        """
        Validate URL for security
        """
        # Basic URL validation
        if not url.startswith(('http://', 'https://')):
            return False
        
        # Prevent SSRF attacks
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            
            # Block private/local addresses
            if parsed.hostname:
                import ipaddress
                try:
                    ip = ipaddress.ip_address(parsed.hostname)
                    if ip.is_private or ip.is_loopback or ip.is_link_local:
                        logger.warning(f"Blocked request to private IP: {parsed.hostname}")
                        return False
                except ValueError:
                    # Not an IP address, check for localhost
                    if parsed.hostname.lower() in ['localhost', '127.0.0.1', '::1']:
                        logger.warning(f"Blocked request to localhost: {parsed.hostname}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating URL {url}: {e}")
            return False
    
    def close(self):
        """Close the session"""
        self.session.close()

class SafeSMTPClient:
    """SMTP client with retry logic and security features"""
    
    def __init__(self, 
                 smtp_server: str,
                 smtp_port: int,
                 username: str,
                 password: str,
                 timeout: int = 30,
                 retry_config: Optional[RetryConfig] = None):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.timeout = timeout
        self.retry_config = retry_config or RetryConfig(max_retries=2)  # Fewer retries for SMTP
    
    @retry_on_network_error()
    def send_email(self, to_addresses: list, subject: str, body: str, 
                   from_address: Optional[str] = None) -> bool:
        """
        Send email with retry logic
        """
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        
        # Validate email addresses
        for addr in to_addresses:
            if not self._is_valid_email(addr):
                raise NetworkError(f"Invalid email address: {addr}")
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = from_address or self.username
        msg['To'] = ', '.join(to_addresses)
        msg['Subject'] = subject
        
        # Sanitize body content
        sanitized_body = self._sanitize_email_content(body)
        msg.attach(MIMEText(sanitized_body, 'html'))
        
        # Send email
        with smtplib.SMTP(self.smtp_server, self.smtp_port, timeout=self.timeout) as server:
            server.starttls()
            server.login(self.username, self.password)
            server.send_message(msg)
        
        logger.info(f"Email sent successfully to {len(to_addresses)} recipients")
        return True
    
    def _is_valid_email(self, email: str) -> bool:
        """Validate email address format"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def _sanitize_email_content(self, content: str) -> str:
        """Sanitize email content to prevent injection"""
        # Remove potentially dangerous content
        import re
        
        # Remove script tags
        content = re.sub(r'<script.*?</script>', '', content, flags=re.IGNORECASE | re.DOTALL)
        
        # Remove javascript: URLs
        content = re.sub(r'javascript:', '', content, flags=re.IGNORECASE)
        
        # Limit content length
        if len(content) > 50000:  # 50KB limit
            content = content[:50000] + "\n\n[Content truncated for security]"
        
        return content

def test_network_connectivity(host: str, port: int, timeout: int = 5) -> bool:
    """
    Test network connectivity to a host and port
    """
    try:
        with socket.create_connection((host, port), timeout=timeout):
            return True
    except (socket.error, socket.timeout):
        return False

def get_public_ip(timeout: int = 10) -> Optional[str]:
    """
    Get public IP address with retry logic
    """
    services = [
        'https://api.ipify.org',
        'https://icanhazip.com',
        'https://ident.me'
    ]
    
    client = SafeHTTPClient(timeout=timeout, retry_config=RetryConfig(max_retries=1))
    
    for service in services:
        try:
            response = client.get(service)
            if response.status_code == 200:
                ip = response.text.strip()
                # Validate IP format
                import ipaddress
                ipaddress.ip_address(ip)
                return ip
        except Exception as e:
            logger.warning(f"Failed to get IP from {service}: {e}")
            continue
    
    return None

# Global HTTP client instance
http_client = SafeHTTPClient()

# Convenience functions
def safe_post(url: str, **kwargs) -> requests.Response:
    """Make a safe POST request"""
    return http_client.post(url, **kwargs)

def safe_get(url: str, **kwargs) -> requests.Response:
    """Make a safe GET request"""
    return http_client.get(url, **kwargs)
