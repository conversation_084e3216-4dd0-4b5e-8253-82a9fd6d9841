import os
from dotenv import load_dotenv
from pathlib import Path
from .path_validator import PathValidator, PathValidationError
import shutil
from datetime import datetime, timedelta

# Load environment variables
load_dotenv('config.env')

class Config:
    """Configuration class for Security Detection System"""

    # Base paths
    BASE_DIR = Path(__file__).parent.parent
    LOG_DIR = BASE_DIR / 'logs'
    ARCHIVE_DIR = LOG_DIR / 'archive'

    # Log files
    EVENTS_LOG = LOG_DIR / 'events_log.jsonl'
    ALERTS_LOG = LOG_DIR / 'alerts.jsonl'
    SYSTEM_LOG = LOG_DIR / 'system.log'

    # Log rotation settings
    LOG_ROTATION_DAYS = int(os.getenv('LOG_ROTATION_DAYS', '30'))
    LOG_CLEANUP_DAYS = int(os.getenv('LOG_CLEANUP_DAYS', '90'))
    MAX_LOG_SIZE_MB = int(os.getenv('MAX_LOG_SIZE_MB', '100'))

    # Azure Configuration
    AZURE_SUBSCRIPTION_ID = os.getenv('AZURE_SUBSCRIPTION_ID')
    AZURE_TENANT_ID = os.getenv('AZURE_TENANT_ID')
    AZURE_CLIENT_ID = os.getenv('AZURE_CLIENT_ID')
    AZURE_CLIENT_SECRET = os.getenv('AZURE_CLIENT_SECRET')

    # Network Configuration
    MONITORED_NETWORKS = os.getenv('MONITORED_NETWORKS', '10.0.0.0/8,***********/16,**********/12').split(',')
    NETWORK_SCAN_INTERVAL = int(os.getenv('NETWORK_SCAN_INTERVAL', '300'))  # 5 minutes

    # Host Configuration
    MONITORED_HOSTS = os.getenv('MONITORED_HOSTS', '').split(',')  # List of hostnames/IPs to monitor
    HOST_SCAN_INTERVAL = int(os.getenv('HOST_SCAN_INTERVAL', '3600'))  # 1 hour

    # Authentication Configuration
    AUTH_LOG_PATHS = os.getenv('AUTH_LOG_PATHS', '/var/log/auth.log,/var/log/secure').split(',')

    # Safe integer conversion with error handling
    try:
        MAX_FAILED_LOGINS = int(os.getenv('MAX_FAILED_LOGINS', '5'))
        if MAX_FAILED_LOGINS <= 0:
            MAX_FAILED_LOGINS = 5
    except (ValueError, TypeError):
        MAX_FAILED_LOGINS = 5

    try:
        UNUSUAL_LOCATION_THRESHOLD_KM = int(os.getenv('UNUSUAL_LOCATION_THRESHOLD_KM', '500'))
        if UNUSUAL_LOCATION_THRESHOLD_KM <= 0:
            UNUSUAL_LOCATION_THRESHOLD_KM = 500
    except (ValueError, TypeError):
        UNUSUAL_LOCATION_THRESHOLD_KM = 500

    # Email Configuration
    SMTP_SERVER = os.getenv('SMTP_SERVER', 'smtp.gmail.com')

    # Safe port conversion
    try:
        SMTP_PORT = int(os.getenv('SMTP_PORT', '587'))
        if not (1 <= SMTP_PORT <= 65535):
            SMTP_PORT = 587
    except (ValueError, TypeError):
        SMTP_PORT = 587

    EMAIL_USERNAME = os.getenv('EMAIL_USERNAME')
    EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD')
    ADMIN_EMAIL = os.getenv('ADMIN_EMAIL')

    # Web Dashboard Configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'azure-sentinel-lite-dev-key-change-in-production')

    # Detection Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')

    # Safe integer conversions for detection settings
    try:
        ALERT_THRESHOLD_MINUTES = int(os.getenv('ALERT_THRESHOLD_MINUTES', '60'))
        if ALERT_THRESHOLD_MINUTES <= 0:
            ALERT_THRESHOLD_MINUTES = 60
    except (ValueError, TypeError):
        ALERT_THRESHOLD_MINUTES = 60

    try:
        ALERT_AGGREGATION_WINDOW = int(os.getenv('ALERT_AGGREGATION_WINDOW', '300'))
        if ALERT_AGGREGATION_WINDOW <= 0:
            ALERT_AGGREGATION_WINDOW = 300
    except (ValueError, TypeError):
        ALERT_AGGREGATION_WINDOW = 300

    # Real-time System Monitoring
    MONITOR_SYSTEM_RESOURCES = os.getenv('MONITOR_SYSTEM_RESOURCES', 'true').lower() == 'true'

    # Safe float conversions for thresholds
    try:
        CPU_THRESHOLD = float(os.getenv('CPU_THRESHOLD', '90.0'))
        if not (0 <= CPU_THRESHOLD <= 100):
            CPU_THRESHOLD = 90.0
    except (ValueError, TypeError):
        CPU_THRESHOLD = 90.0

    try:
        MEMORY_THRESHOLD = float(os.getenv('MEMORY_THRESHOLD', '90.0'))
        if not (0 <= MEMORY_THRESHOLD <= 100):
            MEMORY_THRESHOLD = 90.0
    except (ValueError, TypeError):
        MEMORY_THRESHOLD = 90.0

    try:
        DISK_THRESHOLD = float(os.getenv('DISK_THRESHOLD', '90.0'))
        if not (0 <= DISK_THRESHOLD <= 100):
            DISK_THRESHOLD = 90.0
    except (ValueError, TypeError):
        DISK_THRESHOLD = 90.0

    # File Integrity Monitoring
    MONITOR_FILE_INTEGRITY = os.getenv('MONITOR_FILE_INTEGRITY', 'true').lower() == 'true'
    MONITORED_PATHS = os.getenv('MONITORED_PATHS', '/etc,/bin,/sbin,/usr/bin,/usr/sbin').split(',')
    EXCLUDE_PATTERNS = os.getenv('EXCLUDE_PATTERNS', '*.log,*.tmp,*.pid').split(',')

    # Paths with validation
    try:
        # Initialize path validator for the application
        path_validator = PathValidator(base_directory=Path.cwd())

        # Validate and create safe paths
        LOG_DIR = path_validator.create_safe_path('logs')
        ALERTS_LOG = path_validator.create_safe_path('logs', 'alerts.jsonl')
        EVENTS_LOG = path_validator.create_safe_path('logs', 'events_log.jsonl')
        SYSTEM_LOG = path_validator.create_safe_path('logs', 'system.log')
        FILE_CHANGES_LOG = path_validator.create_safe_path('logs', 'file_changes.log')



        # GeoIP Configuration - validate user-provided path
        geoip_path = os.getenv('GEOIP_DB_PATH', './data/GeoLite2-City.mmdb')
        try:
            GEOIP_DB_PATH = path_validator.validate_path(geoip_path, allow_creation=True, require_extension=False)
        except PathValidationError:
            # Fallback to safe path
            GEOIP_DB_PATH = path_validator.create_safe_path('data', 'GeoLite2-City.mmdb')

    except PathValidationError as e:
        print(f"❌ Path validation error: {e}")
        # Fallback to basic paths
        LOG_DIR = Path('logs')
        ALERTS_LOG = LOG_DIR / 'alerts.jsonl'
        EVENTS_LOG = LOG_DIR / 'events_log.jsonl'
        SYSTEM_LOG = LOG_DIR / 'system.log'
        FILE_CHANGES_LOG = LOG_DIR / 'file_changes.log'

        GEOIP_DB_PATH = Path('./data/GeoLite2-City.mmdb')

    @classmethod
    def validate(cls):
        """Validate required configuration"""
        validation_errors = []

        # Ensure log directory exists
        try:
            cls.LOG_DIR.mkdir(parents=True, exist_ok=True)
        except (OSError, PermissionError) as e:
            validation_errors.append(f"Cannot create log directory: {e}")
            print(f"❌ Critical error: {e}")
            return False

        # Validate Azure credentials
        azure_fields = ['AZURE_SUBSCRIPTION_ID', 'AZURE_TENANT_ID', 'AZURE_CLIENT_ID', 'AZURE_CLIENT_SECRET']
        missing_azure = [f for f in azure_fields if not getattr(cls, f)]
        if missing_azure:
            print(f"⚠️ Azure configuration incomplete: {', '.join(missing_azure)}")
            print("Some features may be limited without Azure credentials")
        else:
            # Test Azure credential format
            try:
                import uuid
                if cls.AZURE_SUBSCRIPTION_ID and cls.AZURE_SUBSCRIPTION_ID != 'your_subscription_id_here':
                    uuid.UUID(cls.AZURE_SUBSCRIPTION_ID)
                if cls.AZURE_TENANT_ID and cls.AZURE_TENANT_ID != 'your_tenant_id_here':
                    uuid.UUID(cls.AZURE_TENANT_ID)
                print("✅ Azure credentials format validated")
            except ValueError:
                print("⚠️ Invalid Azure credential format")
                validation_errors.append("Invalid Azure credential format")

        # Check email configuration with validation
        email_fields = ['EMAIL_USERNAME', 'EMAIL_PASSWORD', 'ADMIN_EMAIL']
        missing_email = [f for f in email_fields if not getattr(cls, f)]
        if missing_email:
            print(f"⚠️ Email configuration incomplete: {', '.join(missing_email)}")
            print("Email notifications will be disabled")

        # Validate email format if provided
        if cls.ADMIN_EMAIL and cls.ADMIN_EMAIL != '<EMAIL>':
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, cls.ADMIN_EMAIL):
                validation_errors.append(f"Invalid admin email format: {cls.ADMIN_EMAIL}")

        # Validate numeric configurations with error handling
        try:
            if cls.SMTP_PORT <= 0 or cls.SMTP_PORT > 65535:
                validation_errors.append(f"Invalid SMTP port: {cls.SMTP_PORT}")
        except (ValueError, TypeError):
            validation_errors.append("SMTP_PORT must be a valid integer")

        try:
            if not (0 <= cls.CPU_THRESHOLD <= 100):
                validation_errors.append(f"CPU_THRESHOLD must be between 0-100: {cls.CPU_THRESHOLD}")
        except (ValueError, TypeError):
            validation_errors.append("CPU_THRESHOLD must be a valid number")

        try:
            if not (0 <= cls.MEMORY_THRESHOLD <= 100):
                validation_errors.append(f"MEMORY_THRESHOLD must be between 0-100: {cls.MEMORY_THRESHOLD}")
        except (ValueError, TypeError):
            validation_errors.append("MEMORY_THRESHOLD must be a valid number")

        # Check GeoIP database
        if not Path(cls.GEOIP_DB_PATH).exists():
            print("⚠️ GeoIP database not found - location detection will be limited")

        # Validate network configuration
        try:
            from ipaddress import ip_network
            for network in cls.MONITORED_NETWORKS:
                if network:
                    ip_network(network)
        except ValueError as e:
            print(f"⚠️ Invalid network configuration: {e}")
            print("Using default private network ranges")
            cls.MONITORED_NETWORKS = ['10.0.0.0/8', '***********/16', '**********/12']

        # Report validation errors
        if validation_errors:
            print("❌ Configuration validation errors:")
            for error in validation_errors:
                print(f"   - {error}")
            return False

        return True

    @classmethod
    def clear_logs(cls):
        """Clear log files"""
        try:
            # Clear log files
            if cls.EVENTS_LOG.exists():
                cls.EVENTS_LOG.unlink()
            if cls.ALERTS_LOG.exists():
                cls.ALERTS_LOG.unlink()
                
            # Create empty files
            cls.EVENTS_LOG.touch()
            cls.ALERTS_LOG.touch()
            return True
        except Exception as e:
            print(f"Error clearing logs: {e}")
            return False
    
    @classmethod
    def rotate_logs(cls, days=30):
        """Rotate logs older than specified days"""
        try:
            # Create archive directory
            archive_dir = cls.LOG_DIR / 'archive'
            archive_dir.mkdir(exist_ok=True)
            
            # Get current timestamp
            now = datetime.now()
            
            # Archive old log files
            for log_file in [cls.EVENTS_LOG, cls.ALERTS_LOG]:
                if log_file.exists():
                    # Check file age
                    file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if now - file_time > timedelta(days=days):
                        # Create archive filename with timestamp
                        archive_name = f"{log_file.stem}_{file_time.strftime('%Y%m%d')}{log_file.suffix}"
                        archive_path = archive_dir / archive_name
                        
                        # Move file to archive
                        shutil.move(str(log_file), str(archive_path))
                        
                        # Create new empty log file
                        log_file.touch()
            
            return True
        except Exception as e:
            print(f"Error rotating logs: {e}")
            return False


