{"timestamp": "2025-05-26T03:36:18.233388+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "*************", "location": {"city": "Amsterdam", "country": "Netherlands", "lat": 52.3676, "lon": 4.9041}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:37:18.233688+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "location": {"city": "Bucharest", "country": "Romania", "lat": 44.4268, "lon": 26.1025}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:38:18.233694+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "*************", "location": {"city": "Amsterdam", "country": "Netherlands", "lat": 52.3676, "lon": 4.9041}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:39:18.233697+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "***************", "location": {"city": "Beijing", "country": "China", "lat": 39.9042, "lon": 116.4074}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:40:18.233700+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "***************", "location": {"city": "Moscow", "country": "Russia", "lat": 55.7558, "lon": 37.6176}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:41:18.233703+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "***************", "location": {"city": "Beijing", "country": "China", "lat": 39.9042, "lon": 116.4074}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:42:18.233706+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "*************", "location": {"city": "Amsterdam", "country": "Netherlands", "lat": 52.3676, "lon": 4.9041}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:43:18.233709+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "***************", "location": {"city": "Beijing", "country": "China", "lat": 39.9042, "lon": 116.4074}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:44:18.233712+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "***************", "location": {"city": "Moscow", "country": "Russia", "lat": 55.7558, "lon": 37.6176}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:45:18.233714+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "**************", "location": {"city": "Unknown", "country": "Unknown", "lat": 0, "lon": 0}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:46:18.233717+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "location": {"city": "Bucharest", "country": "Romania", "lat": 44.4268, "lon": 26.1025}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:47:18.233720+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "location": {"city": "Bucharest", "country": "Romania", "lat": 44.4268, "lon": 26.1025}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:48:18.233722+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "***************", "location": {"city": "Beijing", "country": "China", "lat": 39.9042, "lon": 116.4074}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:49:18.233725+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "***************", "location": {"city": "Beijing", "country": "China", "lat": 39.9042, "lon": 116.4074}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:50:18.233728+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "**************", "location": {"city": "Unknown", "country": "Unknown", "lat": 0, "lon": 0}, "success": false, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T04:06:18.233731+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "location": {"city": "Seattle", "country": "USA", "lat": 47.6062, "lon": -122.3321}, "success": true, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "source": "Azure AD"}
{"timestamp": "2025-05-26T03:56:19.240675+00:00", "type": "role_change", "user": "<EMAIL>", "old_role": "None", "new_role": "Reader", "changed_by": "<EMAIL>", "ip_address": "***************", "source": "Azure RBAC"}
{"timestamp": "2025-05-26T03:58:19.240675+00:00", "type": "role_change", "user": "<EMAIL>", "old_role": "Reader", "new_role": "Contributor", "changed_by": "<EMAIL>", "ip_address": "***************", "source": "Azure RBAC"}
{"timestamp": "2025-05-26T04:00:19.240675+00:00", "type": "role_change", "user": "<EMAIL>", "old_role": "Contributor", "new_role": "User Access Administrator", "changed_by": "<EMAIL>", "ip_address": "***************", "source": "Azure RBAC"}
{"timestamp": "2025-05-26T04:02:19.240675+00:00", "type": "role_change", "user": "<EMAIL>", "old_role": "User Access Administrator", "new_role": "Owner", "changed_by": "<EMAIL>", "ip_address": "***************", "source": "Azure RBAC"}
{"timestamp": "2025-05-26T03:36:20.247807+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "**************", "location": {"city": "New York", "country": "USA", "lat": 40.7128, "lon": -74.006}, "success": true, "source": "Azure AD"}
{"timestamp": "2025-05-26T03:56:20.247865+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "***************", "location": {"city": "Tokyo", "country": "Japan", "lat": 35.6762, "lon": 139.6503}, "success": true, "source": "Azure AD"}
{"timestamp": "2025-05-26T03:21:21.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 22, "protocol": "TCP", "action": "SYN", "result": "blocked"}
{"timestamp": "2025-05-26T03:21:26.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 23, "protocol": "TCP", "action": "SYN", "result": "blocked"}
{"timestamp": "2025-05-26T03:21:31.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 25, "protocol": "TCP", "action": "SYN", "result": "blocked"}
{"timestamp": "2025-05-26T03:21:36.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 53, "protocol": "TCP", "action": "SYN", "result": "blocked"}
{"timestamp": "2025-05-26T03:21:41.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 80, "protocol": "TCP", "action": "SYN", "result": "allowed"}
{"timestamp": "2025-05-26T03:21:46.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 110, "protocol": "TCP", "action": "SYN", "result": "blocked"}
{"timestamp": "2025-05-26T03:21:51.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 135, "protocol": "TCP", "action": "SYN", "result": "blocked"}
{"timestamp": "2025-05-26T03:21:56.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 139, "protocol": "TCP", "action": "SYN", "result": "blocked"}
{"timestamp": "2025-05-26T03:22:01.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 443, "protocol": "TCP", "action": "SYN", "result": "allowed"}
{"timestamp": "2025-05-26T03:22:06.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 993, "protocol": "TCP", "action": "SYN", "result": "blocked"}
{"timestamp": "2025-05-26T03:22:11.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 995, "protocol": "TCP", "action": "SYN", "result": "blocked"}
{"timestamp": "2025-05-26T03:22:16.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 1723, "protocol": "TCP", "action": "SYN", "result": "blocked"}
{"timestamp": "2025-05-26T03:22:21.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 3389, "protocol": "TCP", "action": "SYN", "result": "allowed"}
{"timestamp": "2025-05-26T03:22:26.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 5900, "protocol": "TCP", "action": "SYN", "result": "blocked"}
{"timestamp": "2025-05-26T03:26:21.250856+00:00", "type": "network_access", "source_ip": "**************", "destination_ip": "************", "port": 3389, "protocol": "TCP", "action": "CONNECT", "result": "allowed", "duration": 3600}
{"timestamp": "2025-05-26T02:30:00+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "location": {"city": "Bucharest", "country": "Romania", "lat": 44.4268, "lon": 26.1025}, "success": true, "source": "Azure AD"}
{"timestamp": "2025-05-26T02:30:00+00:00", "type": "user_management", "action": "create_user", "target_user": "<EMAIL>", "performed_by": "<EMAIL>", "ip_address": "************", "source": "Azure AD"}
{"timestamp": "2025-05-26T02:32:00+00:00", "type": "user_management", "action": "create_user", "target_user": "<EMAIL>", "performed_by": "<EMAIL>", "ip_address": "************", "source": "Azure AD"}
{"timestamp": "2025-05-26T02:34:00+00:00", "type": "user_management", "action": "create_user", "target_user": "<EMAIL>", "performed_by": "<EMAIL>", "ip_address": "************", "source": "Azure AD"}
{"timestamp": "2025-05-26T02:36:00+00:00", "type": "user_management", "action": "create_user", "target_user": "<EMAIL>", "performed_by": "<EMAIL>", "ip_address": "************", "source": "Azure AD"}
{"timestamp": "2025-05-26T02:38:00+00:00", "type": "user_management", "action": "create_user", "target_user": "<EMAIL>", "performed_by": "<EMAIL>", "ip_address": "************", "source": "Azure AD"}
{"timestamp": "2025-05-26T02:40:00+00:00", "type": "role_change", "user": "<EMAIL>", "old_role": "None", "new_role": "Contributor", "changed_by": "<EMAIL>", "ip_address": "************", "source": "Azure RBAC"}
{"timestamp": "2025-05-26T02:41:00+00:00", "type": "role_change", "user": "<EMAIL>", "old_role": "None", "new_role": "Contributor", "changed_by": "<EMAIL>", "ip_address": "************", "source": "Azure RBAC"}
{"timestamp": "2025-05-26T02:42:00+00:00", "type": "role_change", "user": "<EMAIL>", "old_role": "None", "new_role": "Contributor", "changed_by": "<EMAIL>", "ip_address": "************", "source": "Azure RBAC"}
{"timestamp": "2025-05-25T15:08:07.894928", "type": "role_change", "resource": "sql-dev-03", "ip_address": "*************", "user": "<EMAIL>", "success": false, "role": "Owner", "previous_role": "Contributor"}
{"timestamp": "2025-05-25T20:12:07.894928", "type": "network_access", "resource": "webapp-dev-02", "ip_address": "************", "user": "<EMAIL>", "success": true, "port": 3306, "protocol": "TCP"}
{"timestamp": "2025-05-24T22:01:07.894928", "type": "network_access", "resource": "sql-dev-03", "ip_address": "************", "user": "<EMAIL>", "success": true, "port": 22, "protocol": "TCP"}
{"timestamp": "2025-05-25T05:45:07.894928", "type": "network_access", "resource": "sql-dev-01", "ip_address": "*************", "user": "<EMAIL>", "success": true, "port": 3306, "protocol": "TCP"}
{"timestamp": "2025-05-25T06:17:07.894928", "type": "vm_access", "resource": "sql-prod-02", "ip_address": "*************", "user": "<EMAIL>", "success": true, "protocol": "SSH", "port": 22}
{"timestamp": "2025-05-25T06:34:07.894928", "type": "network_access", "resource": "sql-prod-02", "ip_address": "**************", "user": "<EMAIL>", "success": true, "port": 3306, "protocol": "TCP"}
{"timestamp": "2025-05-25T19:36:07.894928", "type": "network_access", "resource": "webapp-dev-02", "ip_address": "************", "user": "<EMAIL>", "success": false, "port": 22, "protocol": "TCP"}
{"timestamp": "2025-05-25T10:12:07.894928", "type": "network_access", "resource": "webapp-prod-01", "ip_address": "*************", "user": "<EMAIL>", "success": true, "port": 443, "protocol": "TCP"}
{"timestamp": "2025-05-25T03:51:07.894928", "type": "data_access", "resource": "sql-dev-01", "ip_address": "************", "user": "<EMAIL>", "success": true}
{"timestamp": "2025-05-25T13:24:07.894928", "type": "vm_access", "resource": "sql-prod-02", "ip_address": "*************", "user": "<EMAIL>", "success": true, "protocol": "RDP", "port": 3389}
{"timestamp": "2025-05-25T05:09:07.894928", "type": "network_access", "resource": "sql-prod-02", "ip_address": "***********", "user": "<EMAIL>", "success": true, "port": 443, "protocol": "TCP"}
{"timestamp": "2025-05-25T04:06:07.894928", "type": "vm_access", "resource": "vm-prod-01", "ip_address": "*************", "user": "<EMAIL>", "success": true, "protocol": "SSH", "port": 22}
{"timestamp": "2025-05-25T11:06:07.894928", "type": "role_change", "resource": "webapp-dev-02", "ip_address": "************", "user": "<EMAIL>", "success": false, "role": "Contributor", "previous_role": "Contributor"}
{"timestamp": "2025-05-25T10:09:07.894928", "type": "auth_attempt", "resource": "vm-prod-03", "ip_address": "*************", "user": "<EMAIL>", "success": true}
{"timestamp": "2025-05-25T01:09:07.894928", "type": "vm_access", "resource": "sql-dev-03", "ip_address": "*************", "user": "<EMAIL>", "success": false, "protocol": "RDP", "port": 3389}
{"timestamp": "2025-05-25T18:53:07.894928", "type": "data_access", "resource": "sql-prod-02", "ip_address": "*************", "user": "<EMAIL>", "success": true}
{"timestamp": "2025-05-25T04:31:07.894928", "type": "data_access", "resource": "webapp-prod-02", "ip_address": "************", "user": "<EMAIL>", "success": true}
{"timestamp": "2025-05-25T02:21:07.894928", "type": "auth_attempt", "resource": "sql-prod-02", "ip_address": "************", "user": "<EMAIL>", "success": false}
{"timestamp": "2025-05-25T18:46:07.894928", "type": "network_access", "resource": "vm-dev-02", "ip_address": "************", "user": "<EMAIL>", "success": true, "port": 80, "protocol": "TCP"}
{"timestamp": "2025-05-25T13:36:07.894928", "type": "vm_access", "resource": "sql-prod-02", "ip_address": "************", "user": "<EMAIL>", "success": false, "protocol": "SSH", "port": 22}
{"timestamp": "2025-05-25T14:07:07.896115", "type": "auth_attempt", "user": "<EMAIL>", "ip_address": "************", "location": "eastus", "city": "Virginia", "country": "United States", "coordinates": {"lat": 37.5, "lon": -78.5}, "success": true, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/*********", "auth_method": "AAD Multi-Factor", "app": "Azure Portal", "category": "Sign-in activity", "resource_id": "/tenants/tenant-id-1234/providers/Microsoft.aadiam", "correlation_id": "correlation-7921", "risk_level": "low", "risk_state": "none", "risk_detail": "none"}
{"timestamp": "2025-05-25T19:07:07.896089", "type": "role_change", "user": "<EMAIL>", "ip_address": "*************", "role": "Reader", "scope": "/subscriptions/subscription-1234", "action": "Microsoft.Authorization/roleAssignments/write", "result": "success", "location": "eastus", "user_agent": "Azure CLI/2.50.0 (Python) Azure-SDK-For-Python/1.17.0", "correlation_id": "correlation-6291"}
{"timestamp": "2025-05-25T19:37:07.896089", "type": "role_change", "user": "<EMAIL>", "ip_address": "*************", "role": "Contributor", "role_definition_id": "/providers/Microsoft.Authorization/roleDefinitions/b24988ac-6180-42a0-ab88-20f7382dd24c", "previous_role": "Reader", "scope": "/subscriptions/subscription-1234", "action": "Microsoft.Authorization/roleAssignments/write", "result": "success", "location": "eastus", "correlation_id": "correlation-1899", "additional_properties": {"requestbody": {"properties": {"roleDefinitionId": "/providers/Microsoft.Authorization/roleDefinitions/b24988ac-6180-42a0-ab88-20f7382dd24c", "principalId": "user-principal-id-1234"}}}}
{"timestamp": "2025-05-25T19:52:07.896089", "type": "role_change", "user": "<EMAIL>", "ip_address": "*************", "role": "Owner", "role_definition_id": "/providers/Microsoft.Authorization/roleDefinitions/8e3af657-a8ff-443c-a75c-2fe8c4bcb635", "previous_role": "Contributor", "scope": "/subscriptions/subscription-1234", "action": "Microsoft.Authorization/roleAssignments/write", "result": "success", "location": "eastus", "correlation_id": "correlation-4984", "additional_properties": {"requestbody": {"properties": {"roleDefinitionId": "/providers/Microsoft.Authorization/roleDefinitions/8e3af657-a8ff-443c-a75c-2fe8c4bcb635", "principalId": "user-principal-id-1234"}}}}
{"timestamp": "2025-05-25T20:25:07.895980", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 6379, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:28:07.895980", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 22, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:29:07.895980", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3306, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:31:07.895980", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 22, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:31:07.895980", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 27017, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:32:07.895980", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3389, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:32:07.895980", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 443, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:36:07.895980", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 80, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:36:07.895980", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 1433, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:37:07.895980", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 6379, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:37:07.895980", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 443, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:39:07.895980", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 6379, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:39:07.895980", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 80, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:39:07.896115", "type": "auth_attempt", "user": "<EMAIL>", "ip_address": "************", "location": "northeurope", "city": "Dublin", "country": "Ireland", "coordinates": {"lat": 53.3, "lon": -6.2}, "success": true, "user_agent": "Azure PowerShell/5.9.0", "auth_method": "Password", "app": "Azure PowerShell", "category": "Sign-in activity", "resource_id": "/tenants/tenant-id-1234/providers/Microsoft.aadiam", "correlation_id": "correlation-6317", "risk_level": "high", "risk_state": "confirmed", "risk_detail": "unfamiliarLocationAndProperties", "additional_properties": {"trust_type": "Unmanaged", "network_type": "Unfamiliar network", "conditional_access_status": "success", "conditional_access_policies": ["Default_location_policy"]}}
{"timestamp": "2025-05-25T20:40:07.895980", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3389, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:42:07.895980", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3306, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:43:07.895980", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 27017, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:44:07.896115", "type": "auth_attempt", "user": "<EMAIL>", "ip_address": "*************", "location": "southeastasia", "city": "Singapore", "country": "Singapore", "coordinates": {"lat": 1.3, "lon": 103.8}, "success": true, "user_agent": "Azure CLI/2.50.0", "auth_method": "Password", "app": "Azure CLI", "category": "Sign-in activity", "resource_id": "/tenants/tenant-id-1234/providers/Microsoft.aadiam", "correlation_id": "correlation-7101", "risk_level": "high", "risk_state": "confirmed", "risk_detail": "unfamiliarLocationAndProperties", "additional_properties": {"trust_type": "Unmanaged", "network_type": "Unfamiliar network", "conditional_access_status": "success", "conditional_access_policies": ["Default_location_policy"]}}
{"timestamp": "2025-05-25T20:47:07.895980", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3389, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:47:07.895980", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 27017, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:47:07.895980", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3306, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:51:07.895980", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 443, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:51:07.895980", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 22, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:52:07.895980", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 80, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:52:07.895980", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 1433, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:52:07.895980", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 1433, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T21:07:07.896142", "type": "vm_exposure", "vm_name": "web-server-01", "resource_id": "/subscriptions/subscription-1234/resourceGroups/rg-production-use1/providers/Microsoft.Compute/virtualMachines/web-server-01", "public_ip": "************", "resource_group": "rg-production-use1", "location": "eastus", "subscription_id": "subscription-1234", "vm_size": "Standard_D2s_v3", "os_type": "Linux", "nsg_rule": {"name": "nsg-prod-web", "rule_name": "allow-ssh", "port": 22, "protocol": "TCP", "priority": 100, "direction": "Inbound", "source_address_prefix": "*"}, "security_findings": {"severity": "High", "category": "Network Security", "threat_type": "Exposure", "recommendation": "Remove public IP or restrict NSG rule 'allow-ssh' source address"}}
{"timestamp": "2025-05-25T21:07:07.896142", "type": "vm_exposure", "vm_name": "jump-box", "resource_id": "/subscriptions/subscription-1234/resourceGroups/rg-management-use1/providers/Microsoft.Compute/virtualMachines/jump-box", "public_ip": "*************", "resource_group": "rg-management-use1", "location": "eastus", "subscription_id": "subscription-1234", "vm_size": "Standard_B2ms", "os_type": "Windows", "nsg_rule": {"name": "nsg-mgmt-jumpbox", "rule_name": "allow-rdp", "port": 3389, "protocol": "TCP", "priority": 100, "direction": "Inbound", "source_address_prefix": "*"}, "security_findings": {"severity": "High", "category": "Network Security", "threat_type": "Exposure", "recommendation": "Remove public IP or restrict NSG rule 'allow-rdp' source address"}}
{"timestamp": "2025-05-25T20:28:34.111782", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 22, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:28:34.111782", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 80, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:27:34.111782", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 443, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:25:34.111782", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3389, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:42:34.111782", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 1433, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:43:34.111782", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3306, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:22:34.111782", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 27017, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:38:34.111782", "type": "network_access", "resource": "web-server-prod-01", "resource_type": "Microsoft.Compute/virtualMachines", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 6379, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:32:34.111782", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 22, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:51:34.111782", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 80, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:34:34.111782", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 443, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:23:34.111782", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3389, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:23:34.111782", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 1433, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:30:34.111782", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3306, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:49:34.111782", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 27017, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:45:34.111782", "type": "network_access", "resource": "app-service-api", "resource_type": "Microsoft.Web/sites", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 6379, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:23:34.111782", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 22, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:39:34.111782", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 80, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:38:34.111782", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 443, "protocol": "TCP", "success": true, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:48:34.111782", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3389, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:33:34.111782", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 1433, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:28:34.111782", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 3306, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:49:34.111782", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 27017, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T20:43:34.111782", "type": "network_access", "resource": "aks-cluster-prod", "resource_type": "Microsoft.ContainerService/managedClusters", "subscription_id": "subscription-1234", "resource_group": "rg-production-use1", "ip_address": "************", "port": 6379, "protocol": "TCP", "success": false, "location": "eastus", "network_security_group": "nsg-prod-default"}
{"timestamp": "2025-05-25T19:07:41.824611", "type": "role_change", "user": "<EMAIL>", "ip_address": "*************", "role": "Reader", "scope": "/subscriptions/subscription-1234", "action": "Microsoft.Authorization/roleAssignments/write", "result": "success", "location": "eastus", "user_agent": "Azure CLI/2.50.0 (Python) Azure-SDK-For-Python/1.17.0", "correlation_id": "correlation-8817"}
{"timestamp": "2025-05-25T19:37:41.824611", "type": "role_change", "user": "<EMAIL>", "ip_address": "*************", "role": "Contributor", "role_definition_id": "/providers/Microsoft.Authorization/roleDefinitions/b24988ac-6180-42a0-ab88-20f7382dd24c", "previous_role": "Reader", "scope": "/subscriptions/subscription-1234", "action": "Microsoft.Authorization/roleAssignments/write", "result": "success", "location": "eastus", "correlation_id": "correlation-5782", "additional_properties": {"requestbody": {"properties": {"roleDefinitionId": "/providers/Microsoft.Authorization/roleDefinitions/b24988ac-6180-42a0-ab88-20f7382dd24c", "principalId": "user-principal-id-1234"}}}}
{"timestamp": "2025-05-25T19:52:41.824611", "type": "role_change", "user": "<EMAIL>", "ip_address": "*************", "role": "Owner", "role_definition_id": "/providers/Microsoft.Authorization/roleDefinitions/8e3af657-a8ff-443c-a75c-2fe8c4bcb635", "previous_role": "Contributor", "scope": "/subscriptions/subscription-1234", "action": "Microsoft.Authorization/roleAssignments/write", "result": "success", "location": "eastus", "correlation_id": "correlation-6804", "additional_properties": {"requestbody": {"properties": {"roleDefinitionId": "/providers/Microsoft.Authorization/roleDefinitions/8e3af657-a8ff-443c-a75c-2fe8c4bcb635", "principalId": "user-principal-id-1234"}}}}
{"timestamp": "2025-05-25T21:07:47.927486", "type": "vm_exposure", "vm_name": "web-server-01", "resource_id": "/subscriptions/subscription-1234/resourceGroups/rg-production-use1/providers/Microsoft.Compute/virtualMachines/web-server-01", "public_ip": "************", "resource_group": "rg-production-use1", "location": "eastus", "subscription_id": "subscription-1234", "vm_size": "Standard_D2s_v3", "os_type": "Linux", "nsg_rule": {"name": "nsg-prod-web", "rule_name": "allow-ssh", "port": 22, "protocol": "TCP", "priority": 100, "direction": "Inbound", "source_address_prefix": "*"}, "security_findings": {"severity": "High", "category": "Network Security", "threat_type": "Exposure", "recommendation": "Remove public IP or restrict NSG rule 'allow-ssh' source address"}}
{"timestamp": "2025-05-25T21:07:47.927486", "type": "vm_exposure", "vm_name": "jump-box", "resource_id": "/subscriptions/subscription-1234/resourceGroups/rg-management-use1/providers/Microsoft.Compute/virtualMachines/jump-box", "public_ip": "*************", "resource_group": "rg-management-use1", "location": "eastus", "subscription_id": "subscription-1234", "vm_size": "Standard_B2ms", "os_type": "Windows", "nsg_rule": {"name": "nsg-mgmt-jumpbox", "rule_name": "allow-rdp", "port": 3389, "protocol": "TCP", "priority": 100, "direction": "Inbound", "source_address_prefix": "*"}, "security_findings": {"severity": "High", "category": "Network Security", "threat_type": "Exposure", "recommendation": "Remove public IP or restrict NSG rule 'allow-rdp' source address"}}
