{"timestamp": "2025-05-26T04:06:22.265310+00:00", "type": "port_scan", "severity": "HIGH", "ip_address": "**************", "ports_scanned": [993, 995, 135, 139, 3389, 5900, 110, 80, 53, 22, 23, 25, 443, 1723], "scan_count": 14, "message": "Potential port scan detected from ************** - 14 unique ports"}
{"timestamp": "2025-05-26T04:06:22.266587", "type": "exposed_vm", "vm_name": "web-server-01", "public_ip": "************", "resource_group": "production", "risky_rules": [{"nsg_name": "production-nsg", "rule_name": "allow-ssh", "port": "22", "service": "SSH", "protocol": "TCP", "priority": 1000, "risk_level": "HIGH"}, {"nsg_name": "production-nsg", "rule_name": "allow-http", "port": "80", "service": "Well-known port 80", "protocol": "TCP", "priority": 1010, "risk_level": "MEDIUM"}], "severity": "HIGH", "message": "VM web-server-01 has public IP ************ with risky NSG rules"}
{"timestamp": "2025-05-26T04:06:22.266922", "type": "exposed_vm", "vm_name": "jump-box", "public_ip": "*************", "resource_group": "management", "risky_rules": [{"nsg_name": "management-nsg", "rule_name": "allow-rdp", "port": "3389", "service": "RDP", "protocol": "TCP", "priority": 1000, "risk_level": "HIGH"}], "severity": "HIGH", "message": "VM jump-box has public IP ************* with risky NSG rules"}
{"timestamp": "2025-05-26T04:06:22.267669", "type": "unusual_signin", "user": "<EMAIL>", "ip_address": "***************", "location": {"city": "Tokyo", "country": "Japan", "lat": 35.6762, "lon": 139.6503}, "distance_km": 10851.73, "previous_location": {"city": "New York", "country": "USA", "lat": 40.7128, "lon": -74.006}, "time_difference_hours": 0.33, "severity": "CRITICAL", "message": "Unusual sign-<NAME_EMAIL> from {'city': 'Tokyo', 'country': 'Japan', 'lat': 35.6762, 'lon': 139.6503} (10851.73km from usual location)"}
{"timestamp": "2025-05-26T04:06:22.268435", "type": "privilege_escalation", "user": "<EMAIL>", "previous_role": "Reader", "new_role": "Contributor", "risk_increase": 4, "severity": "MEDIUM", "original_timestamp": "2025-05-26T03:58:19.240675+00:00", "message": "[ALERT] <EMAIL> escalated from Reader to Contributor (Risk increase: 4)"}
{"timestamp": "2025-05-26T04:06:22.268590", "type": "privilege_escalation", "user": "<EMAIL>", "previous_role": "Contributor", "new_role": "User Access Administrator", "risk_increase": 3, "severity": "LOW", "original_timestamp": "2025-05-26T04:00:19.240675+00:00", "message": "[ALERT] <EMAIL> escalated from Contributor to User Access Administrator (Risk increase: 3)"}
{"timestamp": "2025-05-26T04:06:22.268724", "type": "privilege_escalation", "user": "<EMAIL>", "previous_role": "User Access Administrator", "new_role": "Owner", "risk_increase": 1, "severity": "CRITICAL", "original_timestamp": "2025-05-26T04:02:19.240675+00:00", "message": "[ALERT] <EMAIL> escalated from User Access Administrator to Owner (Risk increase: 1)"}
{"timestamp": "2025-05-26T04:06:22.268947", "type": "privilege_escalation", "user": "<EMAIL>", "previous_role": "None", "new_role": "Contributor", "risk_increase": 6, "severity": "MEDIUM", "original_timestamp": "2025-05-26T02:40:00+00:00", "message": "[ALERT] <EMAIL> escalated from None to Contributor (Risk increase: 6)"}
{"timestamp": "2025-05-26T04:06:22.269061", "type": "privilege_escalation", "user": "<EMAIL>", "previous_role": "None", "new_role": "Contributor", "risk_increase": 6, "severity": "MEDIUM", "original_timestamp": "2025-05-26T02:41:00+00:00", "message": "[ALERT] <EMAIL> escalated from None to Contributor (Risk increase: 6)"}
{"timestamp": "2025-05-26T04:06:22.269177", "type": "privilege_escalation", "user": "<EMAIL>", "previous_role": "None", "new_role": "Contributor", "risk_increase": 6, "severity": "MEDIUM", "original_timestamp": "2025-05-26T02:42:00+00:00", "message": "[ALERT] <EMAIL> escalated from None to Contributor (Risk increase: 6)"}
