{"timestamp": "2025-05-26T04:29:35.463322+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 22, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:30:35.463322+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 23, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:31:35.463322+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 24, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:32:35.463322+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 25, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:33:35.463322+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 26, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:34:35.463322+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 27, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:35:35.463322+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 28, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:36:35.463322+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 29, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:37:35.463322+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 30, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:38:35.463322+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 31, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:39:35.463322+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:40:35.463322+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:41:35.463322+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:42:35.463322+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:43:35.463322+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:44:35.463322+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:45:35.463322+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:46:35.463322+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:59:35.463322+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "**************", "success": true, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:54:35.463322+00:00", "type": "role_change", "user": "<EMAIL>", "old_role": "Reader", "new_role": "Owner", "changed_by": "<EMAIL>", "source": "Azure RBAC"}
