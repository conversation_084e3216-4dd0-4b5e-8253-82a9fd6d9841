{"timestamp": "2025-05-26T04:35:05.587565+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 22, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:36:05.587565+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 23, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:37:05.587565+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 24, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:38:05.587565+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 25, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:39:05.587565+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 26, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:40:05.587565+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 27, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:41:05.587565+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 28, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:42:05.587565+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 29, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:43:05.587565+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 30, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:44:05.587565+00:00", "type": "network_access", "source_ip": "*************", "destination_ip": "*********", "port": 31, "protocol": "TCP", "action": "SYN"}
{"timestamp": "2025-05-26T04:45:05.587565+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:46:05.587565+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:47:05.587565+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:48:05.587565+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:49:05.587565+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:50:05.587565+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:51:05.587565+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:52:05.587565+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "success": false, "source": "Azure AD"}
{"timestamp": "2025-05-26T05:05:05.587565+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "**************", "location": {"city": "Tokyo", "country": "Japan", "lat": 35.6762, "lon": 139.6503}, "success": true, "source": "Azure AD"}
{"timestamp": "2025-05-26T03:05:05.587565+00:00", "type": "login", "user": "<EMAIL>", "ip_address": "************", "location": {"city": "New York", "country": "USA", "lat": 40.7128, "lon": -74.006}, "success": true, "source": "Azure AD"}
{"timestamp": "2025-05-26T04:55:05.587565+00:00", "type": "role_change", "user": "<EMAIL>", "old_role": "Reader", "new_role": "Contributor", "changed_by": "<EMAIL>", "source": "Azure RBAC", "message": "Role escalated from Reader to Contributor"}
{"timestamp": "2025-05-26T04:57:05.587565+00:00", "type": "role_change", "user": "<EMAIL>", "old_role": "Contributor", "new_role": "User Access Administrator", "changed_by": "<EMAIL>", "source": "Azure RBAC", "message": "Role escalated from Contributor to User Access Administrator"}
{"timestamp": "2025-05-26T05:00:05.587565+00:00", "type": "role_change", "user": "<EMAIL>", "old_role": "User Access Administrator", "new_role": "Owner", "changed_by": "<EMAIL>", "source": "Azure RBAC", "message": "Role escalated from User Access Administrator to Owner"}
