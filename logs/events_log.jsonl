"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"user_agent"
"source"
"timestamp"
"type"
"user"
"old_role"
"new_role"
"changed_by"
"ip_address"
"source"
"timestamp"
"type"
"user"
"old_role"
"new_role"
"changed_by"
"ip_address"
"source"
"timestamp"
"type"
"user"
"old_role"
"new_role"
"changed_by"
"ip_address"
"source"
"timestamp"
"type"
"user"
"old_role"
"new_role"
"changed_by"
"ip_address"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"source"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"source"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"timestamp"
"type"
"source_ip"
"destination_ip"
"port"
"protocol"
"action"
"result"
"duration"
"timestamp"
"type"
"user"
"ip_address"
"location"
"success"
"source"
"timestamp"
"type"
"action"
"target_user"
"performed_by"
"ip_address"
"source"
"timestamp"
"type"
"action"
"target_user"
"performed_by"
"ip_address"
"source"
"timestamp"
"type"
"action"
"target_user"
"performed_by"
"ip_address"
"source"
"timestamp"
"type"
"action"
"target_user"
"performed_by"
"ip_address"
"source"
"timestamp"
"type"
"action"
"target_user"
"performed_by"
"ip_address"
"source"
"timestamp"
"type"
"user"
"old_role"
"new_role"
"changed_by"
"ip_address"
"source"
"timestamp"
"type"
"user"
"old_role"
"new_role"
"changed_by"
"ip_address"
"source"
"timestamp"
"type"
"user"
"old_role"
"new_role"
"changed_by"
"ip_address"
"source"
