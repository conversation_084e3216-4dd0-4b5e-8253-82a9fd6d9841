{"timestamp": "2025-05-26T04:45:58.138451", "type": "exposed_vm", "vm_name": "web-server-01", "public_ip": "************", "resource_group": "production", "risky_rules": [{"nsg_name": "production-nsg", "rule_name": "allow-ssh", "port": "22", "service": "SSH", "protocol": "TCP", "priority": 1000, "risk_level": "HIGH"}, {"nsg_name": "production-nsg", "rule_name": "allow-http", "port": "80", "service": "Well-known port 80", "protocol": "TCP", "priority": 1010, "risk_level": "MEDIUM"}], "severity": "HIGH", "message": "VM web-server-01 has public IP ************ with risky NSG rules"}
{"timestamp": "2025-05-26T04:45:58.139365", "type": "exposed_vm", "vm_name": "jump-box", "public_ip": "*************", "resource_group": "management", "risky_rules": [{"nsg_name": "management-nsg", "rule_name": "allow-rdp", "port": "3389", "service": "RDP", "protocol": "TCP", "priority": 1000, "risk_level": "HIGH"}], "severity": "HIGH", "message": "VM jump-box has public IP ************* with risky NSG rules"}
