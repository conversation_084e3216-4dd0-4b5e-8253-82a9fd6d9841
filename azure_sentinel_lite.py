#!/usr/bin/env python3
"""
Azure Sentinel Lite - Real-time Security Detection System
A comprehensive security monitoring system for Azure environments
"""

import sys
import time
import json
import signal
import atexit
from datetime import datetime
from pathlib import Path
import argparse

# Add local modules to path
sys.path.append(str(Path(__file__).parent))

from utils.config import Config
from utils.email_notifier import EmailNotifier
from utils.file_operations import cleanup_archives, get_file_stats
from detectors.detect_port_scan import PortScanDetector
from detectors.detect_exposed_vm import ExposedVMDetector
from detectors.detect_unusual_signin import UnusualSignInDetector
from detectors.detect_elevated_activity import ElevatedActivityDetector

class AzureSentinelLite:
    """Main orchestrator for Azure security detection system"""

    def __init__(self):
        print("🔒 Initializing Azure Sentinel Lite...")

        # Validate configuration
        if not Config.validate():
            print("❌ Configuration validation failed")
            sys.exit(1)

        # Register cleanup handlers
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        # Check log sizes and rotate if needed
        self._check_and_rotate_logs()

        # Initialize detectors with method mapping
        self.detectors = {
            'port_scan': PortScanDetector(),
            'exposed_vm': ExposedVMDetector(),
            'unusual_signin': UnusualSignInDetector(),
            'elevated_activity': ElevatedActivityDetector()
        }

        # Dynamic method mapping for detectors
        self.detector_methods = {
            'port_scan': 'detect_port_scans',
            'exposed_vm': 'detect_exposed_vms',
            'unusual_signin': 'detect_unusual_signins',
            'elevated_activity': 'detect_elevated_activity'
        }

        self.email_notifier = EmailNotifier()

        # Ensure log directories exist
        Config.LOG_DIR.mkdir(parents=True, exist_ok=True)

        print("✅ Azure Sentinel Lite initialized successfully")

    def _check_and_rotate_logs(self):
        """Check log sizes and rotate if necessary"""
        for log_file in [Config.EVENTS_LOG, Config.ALERTS_LOG]:
            stats = get_file_stats(log_file)
            if stats['exists'] and stats['size_mb'] > Config.MAX_LOG_SIZE_MB:
                print(f"Log file {log_file.name} exceeds size limit, rotating...")
                self._rotate_log(log_file)

    def _rotate_log(self, log_file: Path):
        """Rotate a specific log file"""
        try:
            # Create archive filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            archive_name = f"{log_file.stem}_{timestamp}{log_file.suffix}"
            archive_path = Config.ARCHIVE_DIR / archive_name

            # Move current log to archive
            log_file.rename(archive_path)

            # Create new empty log file
            log_file.touch()

            print(f"✅ Rotated {log_file.name} to {archive_name}")

            # Cleanup old archives
            cleanup_archives(Config.ARCHIVE_DIR, Config.LOG_CLEANUP_DAYS)

        except Exception as e:
            print(f"Error rotating {log_file.name}: {e}")

    def run_all_detectors(self):
        """Run all security detectors"""
        print("\n🔍 Running security detection scan...")

        all_alerts = []
        detection_summary = {}

        for detector_name, detector in self.detectors.items():
            print(f"\n📊 Running {detector_name.replace('_', ' ').title()} Detector...")

            try:
                # Use dynamic method calling instead of hard-coded if/elif
                method_name = self.detector_methods.get(detector_name)
                if method_name and hasattr(detector, method_name):
                    method = getattr(detector, method_name)
                    alerts = method()
                else:
                    print(f"⚠️ Unknown detector method for {detector_name}")
                    alerts = []

                detection_summary[detector_name] = len(alerts)
                all_alerts.extend(alerts)

            except AttributeError as e:
                print(f"❌ Method not found for {detector_name} detector: {e}")
                detection_summary[detector_name] = 0
            except Exception as e:
                print(f"❌ Error in {detector_name} detector: {e}")
                detection_summary[detector_name] = 0

        # Generate summary report
        self._generate_summary_report(detection_summary, all_alerts)

        return all_alerts

    def run_single_detector(self, detector_name):
        """Run a specific detector"""
        if detector_name not in self.detectors:
            print(f"❌ Unknown detector: {detector_name}")
            print(f"Available detectors: {', '.join(self.detectors.keys())}")
            return []

        print(f"\n🔍 Running {detector_name.replace('_', ' ').title()} Detector...")

        detector = self.detectors[detector_name]

        try:
            # Use dynamic method calling
            method_name = self.detector_methods.get(detector_name)
            if method_name and hasattr(detector, method_name):
                method = getattr(detector, method_name)
                return method()
            else:
                print(f"⚠️ Unknown detector method for {detector_name}")
                return []
        except AttributeError as e:
            print(f"❌ Method not found for {detector_name} detector: {e}")
            return []
        except Exception as e:
            print(f"❌ Error in {detector_name} detector: {e}")
            return []

    def monitor_continuous(self, interval_minutes=5, max_failures=10):
        """Run continuous monitoring with circuit breaker pattern"""
        print(f"\n🔄 Starting continuous monitoring (interval: {interval_minutes} minutes)")
        print("Press Ctrl+C to stop monitoring")

        consecutive_failures = 0
        total_cycles = 0
        max_cycles = 1440  # 24 hours worth of 1-minute cycles (safety limit)

        try:
            while total_cycles < max_cycles:
                total_cycles += 1
                print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Running detection cycle {total_cycles}...")

                try:
                    alerts = self.run_all_detectors()
                    consecutive_failures = 0  # Reset failure counter on success

                    if alerts:
                        print(f"🚨 {len(alerts)} total alerts generated this cycle")
                    else:
                        print("✅ No alerts generated this cycle")

                except Exception as e:
                    consecutive_failures += 1
                    print(f"❌ Error in detection cycle {total_cycles}: {e}")

                    if consecutive_failures >= max_failures:
                        print(f"🛑 Too many consecutive failures ({consecutive_failures}). Stopping monitoring.")
                        break

                    # Exponential backoff for failures
                    failure_delay = min(60 * (2 ** consecutive_failures), 300)  # Max 5 minutes
                    print(f"⏳ Waiting {failure_delay} seconds before retry...")
                    time.sleep(failure_delay)
                    continue

                print(f"💤 Sleeping for {interval_minutes} minutes...")

                # Interruptible sleep
                for _ in range(interval_minutes * 60):
                    time.sleep(1)
                    # Check if we should stop (could be extended with a stop flag)

        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        except Exception as e:
            print(f"\n❌ Critical error in monitoring: {e}")
        finally:
            print(f"\n📊 Monitoring completed after {total_cycles} cycles")

    def _generate_summary_report(self, detection_summary, all_alerts):
        """Generate and display summary report"""
        print("\n" + "="*60)
        print("📋 AZURE SENTINEL LITE - DETECTION SUMMARY")
        print("="*60)

        total_alerts = len(all_alerts)
        print(f"🚨 Total Alerts: {total_alerts}")

        if total_alerts > 0:
            # Count by severity
            severity_counts = {}
            for alert in all_alerts:
                severity = alert.get('severity', 'UNKNOWN')
                severity_counts[severity] = severity_counts.get(severity, 0) + 1

            print("\n📊 Alerts by Severity:")
            for severity, count in sorted(severity_counts.items()):
                print(f"   {severity}: {count}")

            # Count by detector
            print("\n🔍 Alerts by Detector:")
            for detector, count in detection_summary.items():
                if count > 0:
                    print(f"   {detector.replace('_', ' ').title()}: {count}")

            # Show recent critical alerts
            critical_alerts = [a for a in all_alerts if a.get('severity') == 'CRITICAL']
            if critical_alerts:
                print(f"\n🔥 CRITICAL ALERTS ({len(critical_alerts)}):")
                for alert in critical_alerts[-5:]:  # Show last 5
                    print(f"   • {alert.get('message', 'No message')}")

        print("\n📁 Log Files:")
        print(f"   Events: {Config.EVENTS_LOG}")
        print(f"   Alerts: {Config.ALERTS_LOG}")

        print("="*60)

    def show_recent_alerts(self, count=10):
        """Show recent alerts from log file"""
        if not Config.ALERTS_LOG.exists():
            print("No alerts log file found.")
            return

        print(f"\n📋 Last {count} Alerts:")
        print("-" * 50)

        try:
            with open(Config.ALERTS_LOG, 'r') as f:
                lines = f.readlines()

            recent_lines = lines[-count:] if len(lines) >= count else lines

            for line in recent_lines:
                try:
                    alert = json.loads(line)
                    timestamp = alert.get('timestamp', 'Unknown')
                    alert_type = alert.get('type', 'Unknown')
                    severity = alert.get('severity', 'Unknown')
                    message = alert.get('message', 'No message')

                    print(f"{timestamp} | {severity} | {alert_type} | {message}")
                except json.JSONDecodeError:
                    continue

        except Exception as e:
            print(f"Error reading alerts: {e}")

    def cleanup(self):
        """Cleanup resources on exit"""
        print("\nPerforming cleanup...")
        try:
            # Archive logs if they're old enough
            Config.rotate_logs(Config.LOG_ROTATION_DAYS)

            # Clean up old archives
            cleanup_archives(Config.ARCHIVE_DIR, Config.LOG_CLEANUP_DAYS)

        except Exception as e:
            print(f"Error during cleanup: {e}")

    def signal_handler(self, signum, _frame):
        """Handle termination signals"""
        print(f"\nReceived signal {signum}")
        sys.exit(0)

def main():
    parser = argparse.ArgumentParser(description='Azure Sentinel Lite - Security Detection System')
    parser.add_argument('--detector', '-d',
                       choices=['port_scan', 'exposed_vm', 'unusual_signin', 'elevated_activity'],
                       help='Run specific detector only')
    parser.add_argument('--monitor', '-m', action='store_true',
                       help='Run continuous monitoring')
    parser.add_argument('--interval', '-i', type=int, default=5,
                       help='Monitoring interval in minutes (default: 5)')
    parser.add_argument('--recent', '-r', type=int, metavar='N',
                       help='Show N recent alerts')
    parser.add_argument('--web', '-w', action='store_true',
                       help='Start web dashboard interface')

    args = parser.parse_args()

    # Initialize the system
    sentinel = AzureSentinelLite()

    # Start web dashboard if requested
    if args.web:
        print("🌐 Starting Azure Sentinel Lite Web Dashboard...")
        print("📊 Dashboard will be available at: http://localhost:5000")
        print("Press Ctrl+C to stop the web server")

        try:
            import subprocess
            import os

            # Validate executable path
            executable = sys.executable
            if not executable or not Path(executable).exists():
                print("❌ Python executable not found or invalid")
                return

            # Validate script path
            script_path = Path(__file__).parent / "web_dashboard.py"
            if not script_path.exists():
                print("❌ Web dashboard script not found")
                return

            # Resolve paths to prevent directory traversal attacks
            executable = Path(executable).resolve()
            script_path = script_path.resolve()
            working_dir = Path(__file__).parent.resolve()

            # Additional security validation
            if not str(script_path).endswith('web_dashboard.py'):
                print("❌ Invalid script path detected")
                return

            # Secure subprocess execution with comprehensive security measures
            subprocess.run(
                [str(executable), str(script_path)],
                cwd=str(working_dir),
                check=False,  # Don't raise exception on non-zero exit
                timeout=300,  # 5 minute timeout for startup
                env=os.environ.copy(),  # Use clean environment copy
                shell=False,  # CRITICAL: Never use shell=True for security
                stdin=subprocess.DEVNULL,  # Prevent stdin injection attacks
                capture_output=False,  # Allow normal stdout/stderr
                text=True  # Handle text properly
            )
        except subprocess.TimeoutExpired:
            print("⚠️ Web dashboard startup timed out")
        except KeyboardInterrupt:
            print("\n🛑 Web dashboard stopped")
        except FileNotFoundError as e:
            print(f"❌ Required file not found: {e}")
        except PermissionError as e:
            print(f"❌ Permission denied: {e}")
        except subprocess.SubprocessError as e:
            print(f"❌ Subprocess execution failed: {e}")
        except Exception as e:
            print(f"❌ Failed to launch web dashboard: {e}")
        return

    # Show recent alerts if requested
    if args.recent:
        sentinel.show_recent_alerts(args.recent)
        return

    # Run specific detector if requested
    if args.detector:
        sentinel.run_single_detector(args.detector)
        return

    # Run continuous monitoring if requested
    if args.monitor:
        sentinel.monitor_continuous(args.interval)
        return

    # Default: run all detectors once
    sentinel.run_all_detectors()

if __name__ == "__main__":
    main()
