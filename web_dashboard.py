#!/usr/bin/env python3
"""
Azure Sentinel Lite - Web Dashboard
Real-time security monitoring dashboard
"""

import sys
import json
import threading
import time
from datetime import datetime, timedelta, timezone
from pathlib import Path
from collections import defaultdict, Counter

# Add local modules to path
sys.path.append(str(Path(__file__).parent))

from flask import Flask, render_template, jsonify, request
from flask_socketio import Socket<PERSON>, emit
import plotly.graph_objs as go
import plotly.utils
import pandas as pd

from utils.config import Config
from azure_sentinel_lite import AzureSentinelLite

app = Flask(__name__)
app.config['SECRET_KEY'] = Config.SECRET_KEY

# Configure CORS for secure origins
allowed_origins = ['http://localhost:5000', 'https://localhost:5000']
socketio = SocketIO(app, cors_allowed_origins=allowed_origins)

# Thread-safe global state management
import threading
from dataclasses import dataclass
from typing import Optional

@dataclass
class AppState:
    """Thread-safe application state"""
    sentinel_system: Optional[object] = None
    monitoring_active: bool = False
    monitoring_thread: Optional[threading.Thread] = None
    _lock: threading.RLock = threading.RLock()

    def get_sentinel_system(self):
        with self._lock:
            return self.sentinel_system

    def set_sentinel_system(self, system):
        with self._lock:
            self.sentinel_system = system

    def is_monitoring_active(self):
        with self._lock:
            return self.monitoring_active

    def set_monitoring_active(self, active: bool):
        with self._lock:
            self.monitoring_active = active

    def get_monitoring_thread(self):
        with self._lock:
            return self.monitoring_thread

    def set_monitoring_thread(self, thread: Optional[threading.Thread]):
        with self._lock:
            self.monitoring_thread = thread

# Global application state
app_state = AppState()

def load_alerts(max_alerts: int = 1000):
    """Load alerts from log file with memory limits"""
    from utils.file_operations import safe_read_jsonl, FileOperationError

    try:
        # Ensure log directory exists
        Config.LOG_DIR.mkdir(parents=True, exist_ok=True)

        # Use safe file operations with memory limits
        alerts = safe_read_jsonl(Config.ALERTS_LOG, max_lines=max_alerts)

        # Sort by timestamp (most recent first) if we have timestamp data
        try:
            alerts.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        except Exception:
            pass  # If sorting fails, continue with unsorted data

        return alerts

    except FileOperationError as e:
        print(f"File operation error loading alerts: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error loading alerts: {e}")
        return []

def load_events():
    """Load events from log file"""
    events = []
    try:
        # Ensure log directory exists
        Config.LOG_DIR.mkdir(parents=True, exist_ok=True)

        if Config.EVENTS_LOG.exists():
            with open(Config.EVENTS_LOG, 'r') as f:
                for line in f:
                    try:
                        event = json.loads(line)
                        events.append(event)
                    except json.JSONDecodeError:
                        continue
    except Exception as e:
        print(f"Error loading events: {e}")
    return events

def get_dashboard_stats():
    """Get statistics for dashboard"""
    alerts = load_alerts()
    events = load_events()

    # Calculate stats
    total_alerts = len(alerts)
    total_events = len(events)

    # Severity breakdown
    severity_counts = Counter(alert.get('severity', 'UNKNOWN') for alert in alerts)

    # Alert types
    alert_types = Counter(alert.get('type', 'unknown') for alert in alerts)

    # Recent alerts (last 24 hours)
    cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
    recent_alerts = []
    for alert in alerts:
        try:
            alert_time = datetime.fromisoformat(alert['timestamp'].replace('Z', '+00:00'))
            if alert_time > cutoff_time:
                recent_alerts.append(alert)
        except:
            continue

    # Top affected users
    user_counts = Counter()
    for alert in alerts:
        user = alert.get('user', 'unknown')
        if user != 'unknown':
            user_counts[user] += 1

    return {
        'total_alerts': total_alerts,
        'total_events': total_events,
        'recent_alerts_count': len(recent_alerts),
        'severity_counts': dict(severity_counts),
        'alert_types': dict(alert_types),
        'recent_alerts': recent_alerts[-10:],
        'top_users': dict(user_counts.most_common(5))
    }

def create_charts():
    """Create charts for dashboard"""
    alerts = load_alerts()

    # Severity pie chart
    severity_counts = Counter(alert.get('severity', 'UNKNOWN') for alert in alerts)
    severity_fig = go.Figure(data=[go.Pie(
        labels=list(severity_counts.keys()),
        values=list(severity_counts.values()),
        hole=0.3,
        marker_colors=['#dc3545', '#fd7e14', '#ffc107', '#28a745']
    )])
    severity_fig.update_layout(
        title="Alerts by Severity",
        height=400,
        margin=dict(t=50, b=0, l=0, r=0)
    )

    # Alert types bar chart
    alert_types = Counter(alert.get('type', 'unknown') for alert in alerts)
    types_fig = go.Figure(data=[go.Bar(
        x=list(alert_types.keys()),
        y=list(alert_types.values()),
        marker_color='#007bff'
    )])
    types_fig.update_layout(
        title="Alerts by Type",
        height=400,
        margin=dict(t=50, b=50, l=50, r=50),
        xaxis_title="Alert Type",
        yaxis_title="Count"
    )

    # Timeline chart (alerts over time)
    alert_times = []
    for alert in alerts:
        try:
            alert_time = datetime.fromisoformat(alert['timestamp'].replace('Z', '+00:00'))
            alert_times.append(alert_time)
        except:
            continue

    if alert_times:
        # Group by hour
        df = pd.DataFrame({'timestamp': alert_times})
        # Ensure timestamp column is datetime type with UTC timezone handling
        df['timestamp'] = pd.to_datetime(df['timestamp'], utc=True)
        df['hour'] = df['timestamp'].dt.floor('h')
        hourly_counts = df.groupby('hour').size()

        timeline_fig = go.Figure(data=[go.Scatter(
            x=hourly_counts.index,
            y=hourly_counts.values,
            mode='lines+markers',
            line=dict(color='#28a745', width=2),
            marker=dict(size=6)
        )])
        timeline_fig.update_layout(
            title="Alert Timeline (Last 24 Hours)",
            height=400,
            margin=dict(t=50, b=50, l=50, r=50),
            xaxis_title="Time",
            yaxis_title="Alert Count"
        )
    else:
        timeline_fig = go.Figure()
        timeline_fig.update_layout(title="No Alert Data Available", height=400)

    return {
        'severity_chart': json.dumps(severity_fig, cls=plotly.utils.PlotlyJSONEncoder),
        'types_chart': json.dumps(types_fig, cls=plotly.utils.PlotlyJSONEncoder),
        'timeline_chart': json.dumps(timeline_fig, cls=plotly.utils.PlotlyJSONEncoder)
    }

@app.route('/')
def dashboard():
    """Main dashboard page"""
    stats = get_dashboard_stats()
    charts = create_charts()
    return render_template('dashboard.html', stats=stats, charts=charts)

@app.route('/api/stats')
def api_stats():
    """API endpoint for dashboard statistics"""
    return jsonify(get_dashboard_stats())

@app.route('/api/charts')
def api_charts():
    """API endpoint for charts"""
    return jsonify(create_charts())

@app.route('/api/run_detection', methods=['POST'])
def api_run_detection():
    """API endpoint to run detection"""
    try:
        # Validate request data
        if not request.json:
            return jsonify({'success': False, 'error': 'No JSON data provided'}), 400

        detector_type = request.json.get('detector', 'all')

        # Validate detector type
        valid_detectors = ['all', 'port_scan', 'exposed_vm', 'unusual_signin', 'elevated_activity', 'privilege_escalation']
        if detector_type not in valid_detectors:
            return jsonify({'success': False, 'error': f'Invalid detector type: {detector_type}'}), 400

        # Get or create sentinel system thread-safely
        sentinel_system = app_state.get_sentinel_system()
        if not sentinel_system:
            sentinel_system = AzureSentinelLite()
            app_state.set_sentinel_system(sentinel_system)

        if detector_type == 'all':
            alerts = sentinel_system.run_all_detectors()
        else:
            alerts = sentinel_system.run_single_detector(detector_type)

        # Emit real-time update
        socketio.emit('detection_complete', {
            'detector': detector_type,
            'alerts_count': len(alerts),
            'timestamp': datetime.now(timezone.utc).isoformat()
        })

        return jsonify({
            'success': True,
            'alerts_count': len(alerts),
            'message': f'Detection completed: {len(alerts)} alerts generated'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500





@app.route('/api/start_monitoring', methods=['POST'])
def api_start_monitoring():
    """API endpoint to start continuous monitoring"""
    if app_state.is_monitoring_active():
        return jsonify({
            'success': False,
            'message': 'Monitoring is already active'
        })

    app_state.set_monitoring_active(True)
    monitoring_thread = threading.Thread(target=continuous_monitoring)
    monitoring_thread.daemon = True
    monitoring_thread.start()
    app_state.set_monitoring_thread(monitoring_thread)

    return jsonify({
        'success': True,
        'message': 'Continuous monitoring started'
    })

@app.route('/api/stop_monitoring', methods=['POST'])
def api_stop_monitoring():
    """API endpoint to stop continuous monitoring"""
    app_state.set_monitoring_active(False)

    # Wait for thread to finish
    monitoring_thread = app_state.get_monitoring_thread()
    if monitoring_thread:
        monitoring_thread.join(timeout=5)
        app_state.set_monitoring_thread(None)

    return jsonify({
        'success': True,
        'message': 'Continuous monitoring stopped'
    })

def continuous_monitoring():
    """Background thread for continuous monitoring"""
    sentinel_system = app_state.get_sentinel_system()
    if not sentinel_system:
        sentinel_system = AzureSentinelLite()
        app_state.set_sentinel_system(sentinel_system)

    while app_state.is_monitoring_active():
        try:
            print("🔄 Running scheduled detection cycle...")
            alerts = sentinel_system.run_all_detectors()

            # Emit real-time update
            socketio.emit('monitoring_update', {
                'alerts_count': len(alerts),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'status': 'active'
            })

            # Sleep for 5 minutes
            for _ in range(300):  # 5 minutes = 300 seconds
                if not app_state.is_monitoring_active():
                    break
                time.sleep(1)

        except Exception as e:
            print(f"Error in monitoring cycle: {e}")
            socketio.emit('monitoring_error', {
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            })
            time.sleep(60)  # Wait 1 minute before retrying

    socketio.emit('monitoring_stopped', {
        'message': 'Monitoring stopped',
        'timestamp': datetime.now(timezone.utc).isoformat()
    })

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    print('Client connected')
    emit('status', {'message': 'Connected to Azure Sentinel Lite'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print('Client disconnected')



@app.route('/api/clear_test_data', methods=['POST'])
def api_clear_test_data():
    """API endpoint to clear test data"""
    try:
        # Clear log files
        if Config.EVENTS_LOG.exists():
            Config.EVENTS_LOG.unlink()
        if Config.ALERTS_LOG.exists():
            Config.ALERTS_LOG.unlink()

        # Create empty files
        Config.EVENTS_LOG.touch()
        Config.ALERTS_LOG.touch()

        return jsonify({
            'success': True,
            'message': 'Test data cleared successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



@app.route('/api/clear_data', methods=['POST'])
def api_clear_data():
    """API endpoint to clear all logs"""
    try:
        if Config.clear_logs():
            return jsonify({
                'success': True,
                'message': 'Logs cleared successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to clear logs'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/rotate_logs', methods=['POST'])
def api_rotate_logs():
    """API endpoint to rotate old logs"""
    try:
        days = request.json.get('days', 30) if request.json else 30

        if Config.rotate_logs(days):
            return jsonify({
                'success': True,
                'message': f'Logs older than {days} days archived'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to rotate logs'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
