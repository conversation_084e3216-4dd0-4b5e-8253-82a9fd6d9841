"""Generate sample security events and attack scenarios"""
import json
from datetime import datetime, timedelta
import random
from pathlib import Path
from utils.config import Config
from .scenarios import (
    generate_port_scan_scenario,
    generate_privilege_escalation_scenario,
    generate_unusual_signin_scenario,
    generate_exposed_vm_scenario,
    generate_combined_attack_scenario
)

def write_events(events):
    """Write events to log file"""
    Config.LOG_DIR.mkdir(parents=True, exist_ok=True)

    with open(Config.EVENTS_LOG, 'a') as f:
        for event in events:
            f.write(json.dumps(event) + '\n')

def simulate_events(num_events=20):
    """Generate basic security events"""
    events = []
    base_time = datetime.now()

    # Basic event types
    event_types = ['vm_access', 'role_change', 'network_access', 'data_access', 'auth_attempt']

    for i in range(num_events):
        event_type = random.choice(event_types)
        event_time = base_time - timedelta(
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59)
        )

        event = {
            'timestamp': event_time.isoformat(),
            'type': event_type,
            'resource': f"{random.choice(['vm', 'webapp', 'sql'])}-{random.choice(['dev', 'prod'])}-{random.randint(1,3):02d}",
            'ip_address': f"{random.choice(['20.85.', '52.168.', '40.76.'])}{random.randint(0, 255)}.{random.randint(0, 255)}",
            'user': f"user{random.randint(1,5)}@company.com",
            'success': random.random() > 0.3
        }

        # Add event-specific details
        if event_type == 'vm_access':
            event['protocol'] = random.choice(['RDP', 'SSH'])
            event['port'] = 3389 if event['protocol'] == 'RDP' else 22
        elif event_type == 'role_change':
            event['role'] = random.choice(['Reader', 'Contributor', 'Owner'])
            event['previous_role'] = random.choice(['Reader', 'Contributor'])
        elif event_type == 'network_access':
            event['port'] = random.choice([80, 443, 22, 3389, 1433, 3306])
            event['protocol'] = 'TCP'

        events.append(event)

    write_events(events)

    # Auto-backup in demo mode
    if Config.DEMO_MODE:
        Config.backup_demo_data()

    return events

def simulate_attack_scenario(scenario_type=None):
    """Simulate specific attack scenarios"""
    if scenario_type is None:
        # Run combined scenario by default
        events = generate_combined_attack_scenario()
    else:
        # Run specific scenario
        scenario_map = {
            'port_scan': generate_port_scan_scenario,
            'privilege_escalation': generate_privilege_escalation_scenario,
            'unusual_signin': generate_unusual_signin_scenario,
            'exposed_vm': generate_exposed_vm_scenario
        }

        if scenario_type not in scenario_map:
            raise ValueError(f"Unknown scenario type: {scenario_type}")

        events = scenario_map[scenario_type]()

    write_events(events)

    # Auto-backup in demo mode
    if Config.DEMO_MODE:
        Config.backup_demo_data()

    return events

if __name__ == '__main__':
    print("🔄 Generating basic security events...")
    simulate_events(20)

    print("\n🎭 Simulating attack scenarios...")
    simulate_attack_scenario()

    print("✅ Simulation complete")

    print("💾 Data generated and saved to logs")
