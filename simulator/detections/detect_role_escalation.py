import json
from datetime import datetime
from pathlib import Path
import sys
import os

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from utils.config import Config
from utils.email_notifier import EmailNotifier

class PrivilegeEscalationDetector:
    """Enhanced detector for privilege escalation events"""

    def __init__(self):
        self.email_notifier = EmailNotifier()
        self.events_log = Config.EVENTS_LOG
        self.alerts_log = Config.ALERTS_LOG
        self.alerts_log.parent.mkdir(parents=True, exist_ok=True)

        # High-risk roles to monitor
        self.privileged_roles = [
            "Owner", "Contributor", "User Access Administrator",
            "Security Administrator", "Global Administrator",
            "Privileged Role Administrator", "Application Administrator"
        ]

        # Risk scoring for role escalations
        self.role_risk_scores = {
            "Owner": 10,
            "Global Administrator": 10,
            "User Access Administrator": 9,
            "Privileged Role Administrator": 9,
            "Security Administrator": 8,
            "Application Administrator": 7,
            "Contributor": 6,
            "Reader": 2
        }

    def detect_privilege_escalation(self):
        """Detect privilege escalation events with enhanced analysis"""
        alerts = []

        if not self.events_log.exists():
            print("Log file not found.")
            return alerts

        try:
            with open(self.events_log, "r") as f:
                for line in f:
                    try:
                        event = json.loads(line)
                    except json.JSONDecodeError:
                        continue

                    # Handle both 'role_escalation' and 'role_change' event types
                    if event.get("type") in ["role_escalation", "role_change"]:
                        alert = self._analyze_escalation(event)
                        if alert:
                            alerts.append(alert)

                            # Log alert
                            self._log_alert(alert)

                            # Send email notification
                            self.email_notifier.send_alert(alert)

            # Output results
            if alerts:
                print(f"\n🚨 PRIVILEGE ESCALATION ALERTS: {len(alerts)} found")
                for alert in alerts:
                    print(f"{alert['timestamp']} | {alert['message']}")
            else:
                print("✅ No privilege escalation detected.")

            return alerts

        except Exception as e:
            print(f"Error in privilege escalation detection: {e}")
            return []

    def _analyze_escalation(self, event):
        """Analyze escalation event and determine if it's suspicious"""
        new_role = event.get("new_role", "")
        # Handle both 'previous_role' and 'old_role' field names
        prev_role = event.get("previous_role") or event.get("old_role", "")
        user = event.get("user", "unknown")
        timestamp = event.get("timestamp", "")

        # Check if escalation involves privileged roles
        if new_role in self.privileged_roles:
            # Calculate risk score
            prev_score = self.role_risk_scores.get(prev_role, 0)
            new_score = self.role_risk_scores.get(new_role, 0)
            risk_increase = new_score - prev_score

            # Determine severity
            severity = self._calculate_severity(prev_role, new_role, risk_increase)

            return {
                "timestamp": datetime.utcnow().isoformat(),
                "type": "privilege_escalation",
                "user": user,
                "previous_role": prev_role,
                "new_role": new_role,
                "risk_increase": risk_increase,
                "severity": severity,
                "original_timestamp": timestamp,
                "message": f"[ALERT] {user} escalated from {prev_role} to {new_role} (Risk increase: {risk_increase})"
            }

        return None

    def _calculate_severity(self, prev_role, new_role, risk_increase):
        """Calculate severity based on role escalation"""
        if new_role in ["Owner", "Global Administrator"]:
            return "CRITICAL"
        elif risk_increase >= 7:
            return "HIGH"
        elif risk_increase >= 4:
            return "MEDIUM"
        else:
            return "LOW"

    def _log_alert(self, alert):
        """Log alert to file"""
        try:
            with open(self.alerts_log, 'a') as f:
                f.write(json.dumps(alert) + '\n')
        except Exception as e:
            print(f"Error logging alert: {e}")

def main():
    detector = PrivilegeEscalationDetector()
    detector.detect_privilege_escalation()

if __name__ == "__main__":
    main()
