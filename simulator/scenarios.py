"""Predefined test scenarios for Azure Sentinel Lite"""
from datetime import datetime, timedelta
import random
from typing import List, Dict

def generate_port_scan_scenario() -> List[Dict]:
    """Generate a port scan attack scenario"""
    now = datetime.now()
    attacker_ip = "************"  # Mock Azure IP
    target_resources = [
        {"name": "web-server-prod-01", "type": "Microsoft.Compute/virtualMachines"},
        {"name": "app-service-api", "type": "Microsoft.Web/sites"},
        {"name": "aks-cluster-prod", "type": "Microsoft.ContainerService/managedClusters"}
    ]
    
    events = []
    common_ports = [22, 80, 443, 3389, 1433, 3306, 27017, 6379]
    
    for resource in target_resources:
        for port in common_ports:
            events.append({
                'timestamp': (now - timedelta(minutes=random.randint(15, 45))).isoformat(),
                'type': 'network_access',
                'resource': resource['name'],
                'resource_type': resource['type'],
                'subscription_id': 'subscription-1234',
                'resource_group': 'rg-production-use1',
                'ip_address': attacker_ip,
                'port': port,
                'protocol': 'TCP',
                'success': random.random() > 0.7,
                'location': 'eastus',
                'network_security_group': 'nsg-prod-default'
            })
    
    return events

def generate_privilege_escalation_scenario() -> List[Dict]:
    """Generate a privilege escalation attack scenario"""
    now = datetime.now()
    attacker_ip = "*************"  # Mock Azure IP
    compromised_user = "<EMAIL>"
    
    events = []
    
    # Initial access with basic role
    events.append({
        'timestamp': (now - timedelta(hours=2)).isoformat(),
        'type': 'role_change',
        'user': compromised_user,
        'ip_address': attacker_ip,
        'role': 'Reader',
        'scope': '/subscriptions/subscription-1234',
        'action': 'Microsoft.Authorization/roleAssignments/write',
        'result': 'success',
        'location': 'eastus',
        'user_agent': 'Azure CLI/2.50.0 (Python) Azure-SDK-For-Python/1.17.0',
        'correlation_id': f'correlation-{random.randint(1000, 9999)}'
    })
    
    # Multiple role elevations
    roles = [
        {'name': 'Contributor', 'id': '/providers/Microsoft.Authorization/roleDefinitions/b24988ac-6180-42a0-ab88-20f7382dd24c'},
        {'name': 'Owner', 'id': '/providers/Microsoft.Authorization/roleDefinitions/8e3af657-a8ff-443c-a75c-2fe8c4bcb635'}
    ]
    
    for i, role in enumerate(roles):
        events.append({
            'timestamp': (now - timedelta(hours=1, minutes=30-i*15)).isoformat(),
            'type': 'role_change',
            'user': compromised_user,
            'ip_address': attacker_ip,
            'role': role['name'],
            'role_definition_id': role['id'],
            'previous_role': 'Reader' if i == 0 else roles[i-1]['name'],
            'scope': '/subscriptions/subscription-1234',
            'action': 'Microsoft.Authorization/roleAssignments/write',
            'result': 'success',
            'location': 'eastus',
            'correlation_id': f'correlation-{random.randint(1000, 9999)}',
            'additional_properties': {
                'requestbody': {
                    'properties': {
                        'roleDefinitionId': role['id'],
                        'principalId': 'user-principal-id-1234'
                    }
                }
            }
        })
    
    return events

def generate_unusual_signin_scenario() -> List[Dict]:
    """Generate unusual signin patterns"""
    now = datetime.now()
    user = "<EMAIL>"
    
    events = []
    
    # Normal signin patterns from primary location
    locations = [
        {
            'ip': '************',
            'location': 'eastus',
            'city': 'Virginia',
            'country': 'United States',
            'coordinates': {'lat': 37.5, 'lon': -78.5},
            'success': True,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/*********',
            'auth_method': 'AAD Multi-Factor',
            'app': 'Azure Portal'
        }
    ]
    
    # Suspicious signin attempts from unusual locations
    suspicious_locations = [
        {
            'ip': '*************',
            'location': 'southeastasia',
            'city': 'Singapore',
            'country': 'Singapore',
            'coordinates': {'lat': 1.3, 'lon': 103.8},
            'success': True,
            'user_agent': 'Azure CLI/2.50.0',
            'auth_method': 'Password',
            'app': 'Azure CLI'
        },
        {
            'ip': '************',
            'location': 'northeurope',
            'city': 'Dublin',
            'country': 'Ireland',
            'coordinates': {'lat': 53.3, 'lon': -6.2},
            'success': True,
            'user_agent': 'Azure PowerShell/5.9.0',
            'auth_method': 'Password',
            'app': 'Azure PowerShell'
        }
    ]
    
    # Generate normal signin events
    for loc in locations:
        events.append({
            'timestamp': (now - timedelta(hours=random.randint(4, 8))).isoformat(),
            'type': 'auth_attempt',
            'user': user,
            'ip_address': loc['ip'],
            'location': loc['location'],
            'city': loc['city'],
            'country': loc['country'],
            'coordinates': loc['coordinates'],
            'success': loc['success'],
            'user_agent': loc['user_agent'],
            'auth_method': loc['auth_method'],
            'app': loc['app'],
            'category': 'Sign-in activity',
            'resource_id': '/tenants/tenant-id-1234/providers/Microsoft.aadiam',
            'correlation_id': f'correlation-{random.randint(1000, 9999)}',
            'risk_level': 'low',
            'risk_state': 'none',
            'risk_detail': 'none'
        })
    
    # Generate suspicious signin events
    for loc in suspicious_locations:
        events.append({
            'timestamp': (now - timedelta(minutes=random.randint(15, 45))).isoformat(),
            'type': 'auth_attempt',
            'user': user,
            'ip_address': loc['ip'],
            'location': loc['location'],
            'city': loc['city'],
            'country': loc['country'],
            'coordinates': loc['coordinates'],
            'success': loc['success'],
            'user_agent': loc['user_agent'],
            'auth_method': loc['auth_method'],
            'app': loc['app'],
            'category': 'Sign-in activity',
            'resource_id': '/tenants/tenant-id-1234/providers/Microsoft.aadiam',
            'correlation_id': f'correlation-{random.randint(1000, 9999)}',
            'risk_level': 'high',
            'risk_state': 'confirmed',
            'risk_detail': 'unfamiliarLocationAndProperties',
            'additional_properties': {
                'trust_type': 'Unmanaged',
                'network_type': 'Unfamiliar network',
                'conditional_access_status': 'success',
                'conditional_access_policies': ['Default_location_policy']
            }
        })
    
    return events

def generate_exposed_vm_scenario() -> List[Dict]:
    """Generate exposed VM scenario"""
    now = datetime.now()
    
    events = []
    
    # VMs with public IPs and open ports
    exposed_vms = [
        {
            'name': 'web-server-01',
            'ip': '************',
            'resource_group': 'rg-production-use1',
            'location': 'eastus',
            'subscription_id': 'subscription-1234',
            'vm_size': 'Standard_D2s_v3',
            'os_type': 'Linux',
            'nsg': {
                'name': 'nsg-prod-web',
                'rule': 'allow-ssh',
                'port': 22,
                'priority': 100,
                'direction': 'Inbound',
                'source_address_prefix': '*'
            }
        },
        {
            'name': 'jump-box',
            'ip': '*************',
            'resource_group': 'rg-management-use1',
            'location': 'eastus',
            'subscription_id': 'subscription-1234',
            'vm_size': 'Standard_B2ms',
            'os_type': 'Windows',
            'nsg': {
                'name': 'nsg-mgmt-jumpbox',
                'rule': 'allow-rdp',
                'port': 3389,
                'priority': 100,
                'direction': 'Inbound',
                'source_address_prefix': '*'
            }
        }
    ]
    
    for vm in exposed_vms:
        events.append({
            'timestamp': now.isoformat(),
            'type': 'vm_exposure',
            'vm_name': vm['name'],
            'resource_id': f"/subscriptions/{vm['subscription_id']}/resourceGroups/{vm['resource_group']}/providers/Microsoft.Compute/virtualMachines/{vm['name']}",
            'public_ip': vm['ip'],
            'resource_group': vm['resource_group'],
            'location': vm['location'],
            'subscription_id': vm['subscription_id'],
            'vm_size': vm['vm_size'],
            'os_type': vm['os_type'],
            'nsg_rule': {
                'name': vm['nsg']['name'],
                'rule_name': vm['nsg']['rule'],
                'port': vm['nsg']['port'],
                'protocol': 'TCP',
                'priority': vm['nsg']['priority'],
                'direction': vm['nsg']['direction'],
                'source_address_prefix': vm['nsg']['source_address_prefix']
            },
            'security_findings': {
                'severity': 'High',
                'category': 'Network Security',
                'threat_type': 'Exposure',
                'recommendation': f"Remove public IP or restrict NSG rule '{vm['nsg']['rule']}' source address"
            }
        })
    
    return events

def generate_combined_attack_scenario() -> List[Dict]:
    """Generate a complete attack scenario combining multiple patterns"""
    events = []
    
    # Initial port scan
    events.extend(generate_port_scan_scenario())
    
    # Followed by privilege escalation
    events.extend(generate_privilege_escalation_scenario())
    
    # Unusual signin attempts
    events.extend(generate_unusual_signin_scenario())
    
    # Discovery of exposed VMs
    events.extend(generate_exposed_vm_scenario())
    
    return sorted(events, key=lambda x: x['timestamp'])