from azure.identity import ClientSecretCredential
from azure.mgmt.compute import ComputeManagementClient
from azure.mgmt.network import NetworkManagementClient
from azure.mgmt.monitor import MonitorManagementClient
from azure.mgmt.authorization import AuthorizationManagementClient
from azure.mgmt.resource import ResourceManagementClient
from utils.config import Config
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AzureClient:
    """Azure client wrapper for accessing Azure services with rate limiting and pagination"""

    def __init__(self, max_results_per_call=100, rate_limit_delay=0.1):
        # Validate configuration
        Config.validate()

        # Rate limiting and pagination settings
        self.max_results_per_call = max_results_per_call
        self.rate_limit_delay = rate_limit_delay
        self.last_api_call = 0

        # Check if Azure credentials are available and valid
        if not all([Config.AZURE_TENANT_ID, Config.AZURE_CLIENT_ID, Config.AZURE_CLIENT_SECRET, Config.AZURE_SUBSCRIPTION_ID]):
            print("⚠️ Azure credentials not configured - using sample data")
            self.has_azure_config = False
            self.subscription_id = None
            return

        # Check if credentials are placeholder values
        if (Config.AZURE_TENANT_ID in ['your_tenant_id_here', 'demo-tenant-id'] or
            Config.AZURE_CLIENT_ID in ['your_client_id_here', 'demo-client-id'] or
            Config.AZURE_CLIENT_SECRET in ['your_client_secret_here', 'demo-client-secret'] or
            Config.AZURE_SUBSCRIPTION_ID in ['your_subscription_id_here', 'demo-subscription-id']):
            print("⚠️ Azure credentials are placeholder values - using sample data")
            self.has_azure_config = False
            self.subscription_id = None
            return

        # Create credential for Azure access
        self.has_azure_config = True
        try:
            self.credential = ClientSecretCredential(
                tenant_id=Config.AZURE_TENANT_ID,
                client_id=Config.AZURE_CLIENT_ID,
                client_secret=Config.AZURE_CLIENT_SECRET
            )
        except Exception as e:
            print(f"⚠️ Failed to create Azure credentials: {e}")
            print("Using sample data instead")
            self.has_azure_config = False
            self.subscription_id = None
            return

        # Initialize clients
        self.subscription_id = Config.AZURE_SUBSCRIPTION_ID
        self.compute_client = ComputeManagementClient(self.credential, self.subscription_id)
        self.network_client = NetworkManagementClient(self.credential, self.subscription_id)
        self.monitor_client = MonitorManagementClient(self.credential, self.subscription_id)
        self.auth_client = AuthorizationManagementClient(self.credential, self.subscription_id)
        self.resource_client = ResourceManagementClient(self.credential, self.subscription_id)

    def _rate_limit(self):
        """Apply rate limiting between API calls"""
        current_time = time.time()
        time_since_last_call = current_time - self.last_api_call

        if time_since_last_call < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last_call
            time.sleep(sleep_time)

        self.last_api_call = time.time()

    def _safe_api_call(self, api_func, *args, **kwargs):
        """Safely execute Azure API call with error handling and rate limiting"""
        self._rate_limit()

        try:
            return api_func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Azure API call failed: {e}")
            raise

    def get_virtual_machines(self):
        """Get all virtual machines in the subscription"""
        if not self.has_azure_config:
            print("❌ Azure credentials not configured - cannot retrieve VMs")
            return []

        try:
            vms = []
            vm_count = 0
            max_vms = self.max_results_per_call

            vm_iterator = self._safe_api_call(self.compute_client.virtual_machines.list_all)

            for vm in vm_iterator:
                if vm_count >= max_vms:
                    logger.warning(f"Reached maximum VM limit ({max_vms}), stopping collection")
                    break

                try:
                    vm_data = {
                        'name': vm.name,
                        'location': vm.location,
                        'resource_group': vm.id.split('/')[4] if vm.id else 'Unknown',
                        'vm_size': vm.hardware_profile.vm_size if vm.hardware_profile else None,
                        'os_type': vm.storage_profile.os_disk.os_type if vm.storage_profile and vm.storage_profile.os_disk else None
                    }
                    vms.append(vm_data)
                    vm_count += 1

                    # Rate limiting every 10 VMs
                    if vm_count % 10 == 0:
                        time.sleep(0.1)

                except Exception as vm_error:
                    logger.error(f"Error processing VM {getattr(vm, 'name', 'unknown')}: {vm_error}")
                    continue

            logger.info(f"Collected {len(vms)} VMs")
            return vms
        except Exception as e:
            logger.error(f"Error getting VMs: {e}")
            print(f"Error getting VMs: {e}")
            return []

    def get_public_ips(self):
        """Get all public IP addresses"""
        if not self.has_azure_config:
            print("❌ Azure credentials not configured - cannot retrieve public IPs")
            return []

        try:
            public_ips = []
            for rg in self.resource_client.resource_groups.list():
                for pip in self.network_client.public_ip_addresses.list(rg.name):
                    public_ips.append({
                        'name': pip.name,
                        'resource_group': rg.name,
                        'ip_address': pip.ip_address,
                        'allocation_method': pip.public_ip_allocation_method,
                        'associated_resource': pip.ip_configuration.id if pip.ip_configuration else None
                    })
            return public_ips
        except Exception as e:
            print(f"Error getting public IPs: {e}")
            return []

    def get_network_security_groups(self):
        """Get all network security groups and their rules"""
        if not self.has_azure_config:
            print("❌ Azure credentials not configured - cannot retrieve NSGs")
            return []

        try:
            nsgs = []
            for rg in self.resource_client.resource_groups.list():
                for nsg in self.network_client.network_security_groups.list(rg.name):
                    nsg_data = {
                        'name': nsg.name,
                        'resource_group': rg.name,
                        'location': nsg.location,
                        'security_rules': []
                    }

                    # Get security rules
                    if nsg.security_rules:
                        for rule in nsg.security_rules:
                            nsg_data['security_rules'].append({
                                'name': rule.name,
                                'priority': rule.priority,
                                'direction': rule.direction,
                                'access': rule.access,
                                'protocol': rule.protocol,
                                'source_port_range': rule.source_port_range,
                                'destination_port_range': rule.destination_port_range,
                                'source_address_prefix': rule.source_address_prefix,
                                'destination_address_prefix': rule.destination_address_prefix
                            })

                    nsgs.append(nsg_data)
            return nsgs
        except Exception as e:
            print(f"Error getting NSGs: {e}")
            return []

    def get_role_assignments(self):
        """Get role assignments for the subscription"""
        if not self.has_azure_config:
            print("❌ Azure credentials not configured - cannot retrieve role assignments")
            return []

        try:
            assignments = []
            for assignment in self.auth_client.role_assignments.list():
                assignments.append({
                    'principal_id': assignment.principal_id,
                    'role_definition_id': assignment.role_definition_id,
                    'scope': assignment.scope,
                    'principal_type': assignment.principal_type
                })
            return assignments
        except Exception as e:
            print(f"Error getting role assignments: {e}")
            return []
