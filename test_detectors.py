#!/usr/bin/env python3
"""
Test script for Azure Sentinel Lite detectors
Tests real functionality without demo/simulation data
"""

import sys
import json
from datetime import datetime, timezone, timedelta
from pathlib import Path

# Add local modules to path
sys.path.append(str(Path(__file__).parent))

from utils.config import Config
from utils.file_operations import safe_write_jsonl
from detectors.detect_port_scan import PortScanDetector
from detectors.detect_exposed_vm import ExposedVMDetector
from detectors.detect_unusual_signin import UnusualSignInDetector
from detectors.detect_elevated_activity import ElevatedActivityDetector

def create_test_events():
    """Create realistic test events for detector testing"""
    print("📝 Creating test events...")

    # Ensure log directory exists
    Config.LOG_DIR.mkdir(parents=True, exist_ok=True)

    # Clear existing events
    if Config.EVENTS_LOG.exists():
        Config.EVENTS_LOG.unlink()

    # Create realistic test events
    events = []
    base_time = datetime.now(timezone.utc)

    # Port scan events - should trigger port scan detector
    print("   Creating port scan events...")
    for i in range(10):
        event = {
            'timestamp': (base_time - timedelta(minutes=30-i)).isoformat(),
            'type': 'network_access',
            'source_ip': '*************',
            'destination_ip': '*********',
            'port': 22 + i,  # Different ports
            'protocol': 'TCP',
            'action': 'SYN'
        }
        events.append(event)

    # Failed login events - should trigger unusual signin detector
    print("   Creating failed login events...")
    for i in range(8):
        event = {
            'timestamp': (base_time - timedelta(minutes=20-i)).isoformat(),
            'type': 'login',
            'user': '<EMAIL>',
            'ip_address': '************',
            'success': False,
            'source': 'Azure AD'
        }
        events.append(event)

    # Successful login from different location - should trigger unusual signin
    event = {
        'timestamp': base_time.isoformat(),
        'type': 'login',
        'user': '<EMAIL>',
        'ip_address': '**************',
        'location': {
            'city': 'Tokyo',
            'country': 'Japan',
            'lat': 35.6762,
            'lon': 139.6503
        },
        'success': True,
        'source': 'Azure AD'
    }
    events.append(event)

    # Add a baseline login from normal location first
    baseline_event = {
        'timestamp': (base_time - timedelta(hours=2)).isoformat(),
        'type': 'login',
        'user': '<EMAIL>',
        'ip_address': '************',
        'location': {
            'city': 'New York',
            'country': 'USA',
            'lat': 40.7128,
            'lon': -74.0060
        },
        'success': True,
        'source': 'Azure AD'
    }
    events.append(baseline_event)

    # Role change events - should trigger elevated activity detector
    print("   Creating role change events...")
    role_changes = [
        {
            'timestamp': (base_time - timedelta(minutes=10)).isoformat(),
            'type': 'role_change',
            'user': '<EMAIL>',
            'old_role': 'Reader',
            'new_role': 'Contributor',
            'changed_by': '<EMAIL>',
            'source': 'Azure RBAC',
            'message': 'Role escalated from Reader to Contributor'
        },
        {
            'timestamp': (base_time - timedelta(minutes=8)).isoformat(),
            'type': 'role_change',
            'user': '<EMAIL>',
            'old_role': 'Contributor',
            'new_role': 'User Access Administrator',
            'changed_by': '<EMAIL>',
            'source': 'Azure RBAC',
            'message': 'Role escalated from Contributor to User Access Administrator'
        },
        {
            'timestamp': (base_time - timedelta(minutes=5)).isoformat(),
            'type': 'role_change',
            'user': '<EMAIL>',
            'old_role': 'User Access Administrator',
            'new_role': 'Owner',
            'changed_by': '<EMAIL>',
            'source': 'Azure RBAC',
            'message': 'Role escalated from User Access Administrator to Owner'
        }
    ]
    events.extend(role_changes)

    # Write events to log
    for event in events:
        safe_write_jsonl(Config.EVENTS_LOG, event)

    print(f"✅ Created {len(events)} test events")
    return events

def test_port_scan_detector():
    """Test the port scan detector"""
    print("\n🔍 Testing Port Scan Detector...")
    detector = PortScanDetector()
    alerts = detector.detect_port_scans()

    print(f"   Generated {len(alerts)} alerts")
    for alert in alerts:
        print(f"   - {alert.get('message', 'No message')}")

    return alerts

def test_exposed_vm_detector():
    """Test the exposed VM detector"""
    print("\n🔍 Testing Exposed VM Detector...")
    detector = ExposedVMDetector()
    alerts = detector.detect_exposed_vms()

    print(f"   Generated {len(alerts)} alerts")
    for alert in alerts:
        print(f"   - {alert.get('message', 'No message')}")

    return alerts

def test_unusual_signin_detector():
    """Test the unusual signin detector"""
    print("\n🔍 Testing Unusual Signin Detector...")
    detector = UnusualSignInDetector()
    alerts = detector.detect_unusual_signins()

    print(f"   Generated {len(alerts)} alerts")
    for alert in alerts:
        print(f"   - {alert.get('message', 'No message')}")

    return alerts

def test_elevated_activity_detector():
    """Test the elevated activity detector"""
    print("\n🔍 Testing Elevated Activity Detector...")
    detector = ElevatedActivityDetector()
    alerts = detector.detect_elevated_activity()

    print(f"   Generated {len(alerts)} alerts")
    for alert in alerts:
        print(f"   - {alert.get('message', 'No message')}")

    return alerts

def test_all_detectors():
    """Test all detectors"""
    print("🚀 TESTING AZURE SENTINEL LITE DETECTORS")
    print("=" * 60)

    # Create test events
    events = create_test_events()

    # Test each detector
    all_alerts = []

    all_alerts.extend(test_port_scan_detector())
    all_alerts.extend(test_exposed_vm_detector())
    all_alerts.extend(test_unusual_signin_detector())
    all_alerts.extend(test_elevated_activity_detector())

    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Events created: {len(events)}")
    print(f"Total alerts: {len(all_alerts)}")

    if all_alerts:
        # Group by severity
        severity_counts = {}
        for alert in all_alerts:
            severity = alert.get('severity', 'UNKNOWN')
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        print(f"\nAlerts by severity:")
        for severity, count in sorted(severity_counts.items()):
            print(f"  {severity}: {count}")

        # Group by detector type
        detector_counts = {}
        for alert in all_alerts:
            detector_type = alert.get('type', 'unknown')
            detector_counts[detector_type] = detector_counts.get(detector_type, 0) + 1

        print(f"\nAlerts by detector:")
        for detector_type, count in sorted(detector_counts.items()):
            print(f"  {detector_type}: {count}")

    print(f"\n📁 Log files:")
    print(f"  Events: {Config.EVENTS_LOG}")
    print(f"  Alerts: {Config.ALERTS_LOG}")

    return all_alerts

def main():
    """Main test function"""
    try:
        alerts = test_all_detectors()

        if alerts:
            print(f"\n✅ Testing completed successfully - {len(alerts)} alerts generated")
        else:
            print(f"\n⚠️ Testing completed but no alerts were generated")
            print("This might indicate:")
            print("- Detectors are working but no threats detected in test data")
            print("- Azure credentials not configured (some detectors need real Azure data)")
            print("- Detection thresholds not met")

    except Exception as e:
        print(f"\n❌ Testing failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
