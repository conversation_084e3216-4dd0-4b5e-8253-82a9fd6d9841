import json
from datetime import datetime, timedelta
from collections import defaultdict
from pathlib import Path
from azure_collectors.azure_client import AzureClient
from utils.config import Config
from utils.email_notifier import EmailNotifier

class ElevatedActivityDetector:
    """Detector for elevated user role activity"""

    def __init__(self):
        self.azure_client = AzureClient()
        self.email_notifier = EmailNotifier()
        self.events_log = Config.EVENTS_LOG
        self.alerts_log = Config.ALERTS_LOG
        self.alerts_log.parent.mkdir(parents=True, exist_ok=True)

        # Define elevated roles and activities
        self.elevated_roles = [
            'Owner', 'Contributor', 'User Access Administrator',
            'Security Administrator', 'Global Administrator',
            'Privileged Role Administrator'
        ]

        self.sensitive_activities = [
            'role_assignment', 'resource_deletion', 'security_policy_change',
            'network_rule_change', 'key_vault_access', 'admin_consent'
        ]

    def detect_elevated_activity(self):
        """Detect suspicious elevated user activity"""
        alerts = []

        try:
            # Get current role assignments
            role_assignments = self.azure_client.get_role_assignments()

            # Parse activity events
            activity_events = self._parse_activity_events()

            # Analyze elevated user activities
            for user, activities in activity_events.items():
                # Check if user has elevated roles
                user_roles = self._get_user_roles(user, role_assignments)

                if any(role in self.elevated_roles for role in user_roles):
                    # Analyze activity patterns
                    suspicious_patterns = self._analyze_activity_patterns(user, activities, user_roles)

                    for pattern in suspicious_patterns:
                        alert = {
                            'timestamp': datetime.utcnow().isoformat(),
                            'type': 'elevated_activity',
                            'user': user,
                            'user_roles': user_roles,
                            'suspicious_pattern': pattern['pattern_type'],
                            'activity_count': pattern['activity_count'],
                            'time_window_hours': pattern['time_window_hours'],
                            'activities': pattern['activities'],
                            'severity': pattern['severity'],
                            'message': f"Suspicious elevated activity detected for {user}: {pattern['pattern_type']}"
                        }
                        alerts.append(alert)

                        # Log alert
                        self._log_alert(alert)

                        # Send email notification
                        alert['user'] = user  # Ensure user is in alert data for email routing
                        self.email_notifier.send_alert(alert)

            # Output results
            if alerts:
                print(f"\n🚨 ELEVATED ACTIVITY ALERTS: {len(alerts)} found")
                for alert in alerts:
                    print(f"{alert['timestamp']} | {alert['message']}")
            else:
                print("✅ No suspicious elevated activity detected.")

            return alerts

        except Exception as e:
            print(f"Error in elevated activity detection: {e}")
            return []

    def _parse_activity_events(self):
        """Parse activity events from logs"""
        activity_events = defaultdict(list)

        if not self.events_log.exists():
            return activity_events

        try:
            with open(self.events_log, 'r') as f:
                for line in f:
                    try:
                        event = json.loads(line)

                        # Look for role escalation and other elevated activities
                        if event.get('type') in ['role_escalation'] + self.sensitive_activities:
                            user = event.get('user', 'unknown')
                            activity_events[user].append(event)

                    except json.JSONDecodeError:
                        continue
        except Exception as e:
            print(f"Error parsing activity events: {e}")

        return activity_events

    def _get_user_roles(self, user, role_assignments):
        """Get roles for a specific user"""
        # This is a simplified implementation
        # In a real scenario, you'd need to resolve principal_id to user email
        user_roles = []

        # Mock role data based on user patterns
        if 'admin' in user.lower():
            user_roles = ['User Access Administrator', 'Contributor']
        elif '@' in user:
            user_roles = ['Contributor']

        return user_roles

    def _analyze_activity_patterns(self, user, activities, user_roles):
        """Analyze user activities for suspicious patterns"""
        suspicious_patterns = []

        if not activities:
            return suspicious_patterns

        # Sort activities by timestamp
        activities.sort(key=lambda x: x.get('timestamp', ''))

        # Pattern 1: High frequency of role changes
        role_changes = [a for a in activities if a.get('type') == 'role_escalation']
        if len(role_changes) >= 3:
            time_span = self._calculate_time_span(role_changes)
            if time_span <= 24:  # 3+ role changes in 24 hours
                suspicious_patterns.append({
                    'pattern_type': 'Rapid role escalation',
                    'activity_count': len(role_changes),
                    'time_window_hours': time_span,
                    'activities': [a['message'] for a in role_changes],
                    'severity': 'HIGH'
                })

        # Pattern 2: After-hours activity
        after_hours_activities = self._find_after_hours_activities(activities)
        if len(after_hours_activities) >= 2:
            suspicious_patterns.append({
                'pattern_type': 'After-hours elevated activity',
                'activity_count': len(after_hours_activities),
                'time_window_hours': self._calculate_time_span(after_hours_activities),
                'activities': [a['message'] for a in after_hours_activities],
                'severity': 'MEDIUM'
            })

        # Pattern 3: Unusual volume of activities
        recent_activities = self._get_recent_activities(activities, hours=24)
        if len(recent_activities) >= 10:
            suspicious_patterns.append({
                'pattern_type': 'High volume of elevated activities',
                'activity_count': len(recent_activities),
                'time_window_hours': 24,
                'activities': [a['message'] for a in recent_activities[-5:]],  # Last 5 activities
                'severity': 'MEDIUM'
            })

        # Pattern 4: Privilege escalation to highest roles
        highest_role_escalations = [
            a for a in role_changes
            if a.get('new_role') in ['Owner', 'User Access Administrator', 'Global Administrator']
        ]
        if highest_role_escalations:
            suspicious_patterns.append({
                'pattern_type': 'Escalation to highest privilege roles',
                'activity_count': len(highest_role_escalations),
                'time_window_hours': self._calculate_time_span(highest_role_escalations),
                'activities': [a['message'] for a in highest_role_escalations],
                'severity': 'CRITICAL'
            })

        return suspicious_patterns

    def _calculate_time_span(self, activities):
        """Calculate time span of activities in hours"""
        if len(activities) < 2:
            return 0

        try:
            timestamps = [datetime.fromisoformat(a['timestamp'].replace('Z', '+00:00')) for a in activities]
            timestamps.sort()
            time_span = (timestamps[-1] - timestamps[0]).total_seconds() / 3600
            return round(time_span, 2)
        except:
            return 0

    def _find_after_hours_activities(self, activities):
        """Find activities that occurred after business hours"""
        after_hours = []

        for activity in activities:
            try:
                timestamp = datetime.fromisoformat(activity['timestamp'].replace('Z', '+00:00'))
                hour = timestamp.hour

                # Consider after hours as before 8 AM or after 6 PM
                if hour < 8 or hour > 18:
                    after_hours.append(activity)
            except:
                continue

        return after_hours

    def _get_recent_activities(self, activities, hours=24):
        """Get activities from the last N hours"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent = []

        for activity in activities:
            try:
                timestamp = datetime.fromisoformat(activity['timestamp'].replace('Z', '+00:00'))
                if timestamp > cutoff_time:
                    recent.append(activity)
            except:
                continue

        return recent

    def _log_alert(self, alert):
        """Log alert to file"""
        try:
            with open(self.alerts_log, 'a') as f:
                f.write(json.dumps(alert) + '\n')
        except Exception as e:
            print(f"Error logging alert: {e}")

def main():
    detector = ElevatedActivityDetector()
    detector.detect_elevated_activity()

if __name__ == "__main__":
    main()
