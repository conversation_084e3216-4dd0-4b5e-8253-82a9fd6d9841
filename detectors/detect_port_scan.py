from datetime import datetime, timedelta
from collections import defaultdict
import json
from pathlib import Path
from utils.config import Config
from utils.file_operations import safe_read_jsonl, safe_write_jsonl, FileOperationError
from utils.timezone_handler import now_utc, to_utc, is_within_window

class PortScanDetector:
    """Detector for port scanning activities"""

    def __init__(self):
        self.events_log = Config.EVENTS_LOG
        self.alerts_log = Config.ALERTS_LOG
        self.scan_threshold = 5  # Number of different ports to trigger alert
        self.time_window = timedelta(minutes=Config.ALERT_THRESHOLD_MINUTES)

    def detect_port_scans(self):
        """Detect potential port scanning activity"""
        alerts = []

        print("🔍 Running Port Scan Detection")

        try:
            # Read events from log
            events = self._load_events()

            # Group events by source IP
            ip_activities = defaultdict(lambda: defaultdict(list))

            for event in events:
                if event['type'] == 'network_access':
                    # Handle both 'ip_address' and 'source_ip' fields
                    ip = event.get('source_ip') or event.get('ip_address')
                    if not ip:
                        continue

                    # Use timezone-aware timestamp parsing
                    timestamp = to_utc(event['timestamp'])
                    port = event.get('port')
                    if port:
                        ip_activities[ip][timestamp].append(port)

            now = now_utc()
            for ip, timestamps in ip_activities.items():
                window_start = now - self.time_window

                ports_in_window = set()
                for timestamp, ports in timestamps.items():
                    if timestamp >= window_start:
                        ports_in_window.update(ports)

                if len(ports_in_window) >= self.scan_threshold:
                    alert = {
                        'timestamp': now.isoformat(),
                        'type': 'port_scan',
                        'severity': 'HIGH',
                        'ip_address': ip,
                        'ports_scanned': list(ports_in_window),
                        'scan_count': len(ports_in_window),
                        'message': f"Potential port scan detected from {ip} - {len(ports_in_window)} unique ports"
                    }

                    print(f"📢 Port scan detected from {ip}")
                    print(f"   - Scanned Ports: {', '.join(map(str, sorted(ports_in_window)))}")
                    print(f"   - Time Window: {Config.ALERT_THRESHOLD_MINUTES} minutes")

                    alerts.append(alert)
                    self._log_alert(alert)

        except Exception as e:
            print(f"Error in port scan detection: {e}")
            return []

        return alerts

    def _load_events(self):
        """Load events from log file with safe operations"""
        try:
            # Limit to last 1000 events to prevent memory issues
            events = safe_read_jsonl(self.events_log, max_lines=1000)
            return events
        except FileOperationError as e:
            print(f"Error loading events: {e}")
            return []
        except Exception as e:
            print(f"Unexpected error loading events: {e}")
            return []

    def _log_alert(self, alert):
        """Log generated alert with safe operations"""
        try:
            # Validate alert has required fields
            required_fields = ['timestamp', 'type', 'severity']
            if not all(field in alert for field in required_fields):
                print(f"⚠️ Alert missing required fields: {alert}")
                return False

            success = safe_write_jsonl(self.alerts_log, alert)
            if not success:
                print(f"❌ Failed to write alert to log")
            return success

        except FileOperationError as e:
            print(f"Error logging alert: {e}")
            return False
        except Exception as e:
            print(f"Unexpected error logging alert: {e}")
            return False

def main():
    detector = PortScanDetector()
    detector.detect_port_scans()

if __name__ == "__main__":
    main()
