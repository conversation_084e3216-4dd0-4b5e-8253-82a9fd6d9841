import json
import requests
from datetime import datetime, timedelta
from collections import defaultdict
from pathlib import Path
from utils.config import Config
from utils.email_notifier import EmailNotifier

class UnusualSignInDetector:
    """Detector for unusual sign-in locations"""

    def __init__(self):
        self.email_notifier = EmailNotifier()
        self.events_log = Config.EVENTS_LOG
        self.alerts_log = Config.ALERTS_LOG
        self.alerts_log.parent.mkdir(parents=True, exist_ok=True)
        self.location_threshold_km = float(Config.UNUSUAL_LOCATION_THRESHOLD_KM)

        # User location history (in production, this would be in a database)
        self.user_locations = {}
        self.high_risk_countries = self._load_high_risk_countries()
        self.timezone_map = self._load_timezone_data()

    def detect_unusual_signins(self):
        """Detect sign-ins from unusual locations"""
        alerts = []

        if not self.events_log.exists():
            print("Events log file not found.")
            return alerts

        try:
            # Parse sign-in events
            signin_events = []

            with open(self.events_log, 'r') as f:
                for line in f:
                    try:
                        event = json.loads(line)
                        # Handle both old format and new format
                        if (event.get('type') in ['failed_login', 'successful_login'] or
                            (event.get('type') == 'login' and event.get('success') == True)):
                            signin_events.append(event)
                    except json.JSONDecodeError:
                        continue

            # Group by user and analyze locations
            user_events = defaultdict(list)
            for event in signin_events:
                user = event.get('user', 'unknown')
                user_events[user].append(event)

            # Analyze each user's sign-in patterns
            for user, events in user_events.items():
                unusual_signins = self._analyze_user_locations(user, events)

                for unusual_signin in unusual_signins:
                    alert = {
                        'timestamp': datetime.utcnow().isoformat(),
                        'type': 'unusual_signin',
                        'user': user,
                        'ip_address': unusual_signin['ip'],
                        'location': unusual_signin['location'],
                        'distance_km': unusual_signin['distance_km'],
                        'previous_location': unusual_signin['previous_location'],
                        'time_difference_hours': unusual_signin['time_diff_hours'],
                        'severity': self._calculate_severity(unusual_signin),
                        'message': f"Unusual sign-in for {user} from {unusual_signin['location']} ({unusual_signin['distance_km']}km from usual location)"
                    }
                    alerts.append(alert)

                    # Log alert
                    self._log_alert(alert)

                    # Send email notification (to both admin and user)
                    alert['user'] = user  # Ensure user is in alert data for email routing
                    self.email_notifier.send_alert(alert)

            # Output results
            if alerts:
                print(f"\n🚨 UNUSUAL SIGN-IN ALERTS: {len(alerts)} found")
                for alert in alerts:
                    print(f"{alert['timestamp']} | {alert['message']}")
            else:
                print("✅ No unusual sign-in locations detected.")

            return alerts

        except Exception as e:
            print(f"Error in unusual sign-in detection: {e}")
            return []

    def _analyze_user_locations(self, user, events):
        """Analyze user's sign-in locations for anomalies"""
        unusual_signins = []

        # Sort events by timestamp
        events.sort(key=lambda x: x.get('timestamp', ''))

        for event in events:
            # Handle both 'ip' and 'ip_address' fields
            ip = event.get('ip') or event.get('ip_address', '')
            timestamp = event.get('timestamp', '')

            if not ip or not timestamp:
                continue

            # Use location from event if available, otherwise get from IP
            location = event.get('location')
            if not location or not isinstance(location, dict):
                location = self._get_ip_location(ip)

            if not location:
                continue

            # Check for high-risk countries
            if location.get('country') in self.high_risk_countries:
                unusual_signins.append({
                    'ip': ip,
                    'location': location,
                    'risk_factor': 'High-risk country',
                    'severity': 'HIGH'
                })
                continue

            # Check against user's previous locations
            if user in self.user_locations:
                for prev_location in self.user_locations[user]:
                    distance = self._calculate_distance(location, prev_location['location'])
                    time_diff = self._calculate_time_difference(timestamp, prev_location['timestamp'])

                    if distance > self.location_threshold_km:
                        # Calculate required travel time based on mode of transport
                        max_speed = self._calculate_max_speed(distance)
                        required_time = distance / max_speed

                        # Check timezone difference
                        tz_diff = self._get_timezone_difference(
                            location.get('lon'),
                            location.get('lat'),
                            prev_location['location'].get('lon'),
                            prev_location['location'].get('lat')
                        )

                        # Adjust time difference for timezone change
                        adjusted_time_diff = abs(time_diff - tz_diff)

                        if adjusted_time_diff < required_time:
                            unusual_signins.append({
                                'ip': ip,
                                'location': location,
                                'distance_km': round(distance, 2),
                                'previous_location': prev_location['location'],
                                'time_diff_hours': round(adjusted_time_diff, 2),
                                'impossible_travel': True,
                                'severity': 'CRITICAL'
                            })
                        elif distance > self.location_threshold_km * 2:
                            unusual_signins.append({
                                'ip': ip,
                                'location': location,
                                'distance_km': round(distance, 2),
                                'previous_location': prev_location['location'],
                                'time_diff_hours': round(adjusted_time_diff, 2),
                                'impossible_travel': False,
                                'severity': 'HIGH'
                            })

            # Update user's location history
            if user not in self.user_locations:
                self.user_locations[user] = []
            self.user_locations[user].append({
                'timestamp': timestamp,
                'location': location
            })

        return unusual_signins

    def _calculate_max_speed(self, distance):
        """Calculate maximum realistic travel speed based on distance"""
        # For very short distances (< 100km), assume car travel (120 km/h)
        if distance < 100:
            return 120
        # For medium distances (< 500km), assume high-speed rail (300 km/h)
        elif distance < 500:
            return 300
        # For long distances, assume commercial flight (900 km/h)
        else:
            return 900

    def _get_timezone_difference(self, lon1, lat1, lon2, lat2):
        """Calculate timezone difference between two locations"""
        try:
            tz1 = self._get_timezone_from_coords(lon1, lat1)
            tz2 = self._get_timezone_from_coords(lon2, lat2)
            return tz1 - tz2
        except:
            return 0  # Return 0 if timezone calculation fails

    def _get_ip_location(self, ip):
        """Get geographic location for IP address"""
        try:
            # Use a free IP geolocation service
            response = requests.get(f"http://ip-api.com/json/{ip}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    return {
                        'city': data.get('city', 'Unknown'),
                        'country': data.get('country', 'Unknown'),
                        'lat': data.get('lat', 0),
                        'lon': data.get('lon', 0)
                    }
        except:
            pass

        # Fallback to mock data for testing
        mock_locations = {
            '***********': {'city': 'Seattle', 'country': 'USA', 'lat': 47.6062, 'lon': -122.3321},
            '********': {'city': 'New York', 'country': 'USA', 'lat': 40.7128, 'lon': -74.0060},
            '*********': {'city': 'London', 'country': 'UK', 'lat': 51.5074, 'lon': -0.1278},
            '*********': {'city': 'Moscow', 'country': 'Russia', 'lat': 55.7558, 'lon': 37.6176}
        }

        return mock_locations.get(ip, {'city': 'Unknown', 'country': 'Unknown', 'lat': 0, 'lon': 0})

    def _calculate_distance(self, loc1, loc2):
        """Calculate distance between two locations in kilometers"""
        import math

        lat1, lon1 = math.radians(loc1['lat']), math.radians(loc1['lon'])
        lat2, lon2 = math.radians(loc2['lat']), math.radians(loc2['lon'])

        dlat = lat2 - lat1
        dlon = lon2 - lon1

        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))

        # Earth's radius in kilometers
        r = 6371

        return r * c

    def _calculate_time_difference(self, timestamp1, timestamp2):
        """Calculate time difference in hours"""
        try:
            dt1 = datetime.fromisoformat(timestamp1.replace('Z', '+00:00'))
            dt2 = datetime.fromisoformat(timestamp2.replace('Z', '+00:00'))
            return abs((dt1 - dt2).total_seconds()) / 3600
        except:
            return 0

    def _calculate_severity(self, signin_data):
        """Calculate severity based on sign-in characteristics"""
        if signin_data.get('impossible_travel'):
            return 'CRITICAL'
        elif signin_data['distance_km'] > 5000:
            return 'HIGH'
        elif signin_data['distance_km'] > 1000:
            return 'MEDIUM'
        else:
            return 'LOW'

    def _cleanup_old_locations(self, user):
        """Remove old location data"""
        cutoff_date = datetime.utcnow() - timedelta(days=30)

        self.user_locations[user] = [
            loc for loc in self.user_locations[user]
            if datetime.fromisoformat(loc['timestamp'].replace('Z', '+00:00')) > cutoff_date
        ]

    def _log_alert(self, alert):
        """Log alert to file"""
        try:
            with open(self.alerts_log, 'a') as f:
                f.write(json.dumps(alert) + '\n')
        except Exception as e:
            print(f"Error logging alert: {e}")

    def _load_high_risk_countries(self):
        """Load list of high-risk countries from config or external source"""
        # For simplicity, define a static list here
        return {'Russia', 'China', 'Iran', 'North Korea'}

    def _load_timezone_data(self):
        """Load timezone data for IP geolocation"""
        # For simplicity, return a static mapping here
        return {
            'Seattle': -8,
            'New York': -5,
            'London': 0,
            'Moscow': 3
        }

def main():
    detector = UnusualSignInDetector()
    detector.detect_unusual_signins()

if __name__ == "__main__":
    main()
